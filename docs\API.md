# KodeXGuard API Documentation

## Overview

KodeXGuard menyediakan RESTful API yang komprehensif untuk semua fitur cybersecurity platform. API menggunakan JSON untuk request dan response, dengan autentikasi berbasis JWT dan API Key.

## Base URL
```
Production: https://api.kodexguard.com
Development: http://localhost:3000/api
```

## Authentication

### JWT Token
Untuk operasi yang memerlukan autentikasi user:
```http
Authorization: Bearer <jwt_token>
```

### API Key
Untuk akses programmatic:
```http
X-API-Key: <api_key>
```

## Rate Limiting

Rate limit berdasarkan plan user:
- **Free**: 50 requests/day
- **Student**: 200 requests/day  
- **Hobby**: 500 requests/day
- **Bug Hunter**: 2000 requests/day
- **Cybersecurity**: Unlimited

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

## Authentication Endpoints

### POST /auth/login
Login user dan mendapatkan JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "role": "user",
      "plan": "hobby"
    },
    "token": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresAt": "2025-01-01T00:00:00Z"
  }
}
```

### POST /auth/register
Registrasi user baru.

**Request:**
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "New User"
}
```

### POST /auth/refresh
Refresh JWT token menggunakan refresh token.

### GET /auth/profile
Mendapatkan profil user yang sedang login.

### POST /auth/api-key/generate
Generate API key baru untuk user.

## OSINT Endpoints

### POST /osint/search
Melakukan pencarian OSINT.

**Request:**
```json
{
  "type": "nik|npwp|phone|imei|email|domain|name|address",
  "query": "search_query",
  "options": {
    "deepSearch": true,
    "includeSocialMedia": true,
    "includeDataBreaches": true,
    "maxResults": 100
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "search_id",
    "type": "nik",
    "query": "1234567890123456",
    "status": "completed",
    "results": [
      {
        "source": "dukcapil",
        "data": {
          "name": "John Doe",
          "birthDate": "1990-01-01",
          "address": "Jakarta"
        },
        "confidence": 0.95
      }
    ],
    "createdAt": "2025-01-01T00:00:00Z",
    "completedAt": "2025-01-01T00:01:00Z"
  }
}
```

### GET /osint/search/{id}
Mendapatkan hasil pencarian OSINT berdasarkan ID.

### GET /osint/history
Mendapatkan riwayat pencarian OSINT user.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `type`: Filter by search type
- `status`: Filter by status

## Vulnerability Scanner Endpoints

### POST /scanner/scan
Memulai vulnerability scan.

**Request:**
```json
{
  "targetUrl": "https://example.com",
  "scanType": "full|quick|custom",
  "options": {
    "checkSQLi": true,
    "checkXSS": true,
    "checkLFI": true,
    "checkRCE": true,
    "maxDepth": 3,
    "timeout": 300
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "scan_id",
    "targetUrl": "https://example.com",
    "status": "running",
    "progress": 0,
    "estimatedTime": 600,
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

### GET /scanner/scan/{id}
Mendapatkan status dan hasil scan.

### POST /scanner/scan/{id}/cancel
Membatalkan scan yang sedang berjalan.

### GET /scanner/history
Mendapatkan riwayat scan user.

## File Analysis Endpoints

### POST /file/analyze
Upload dan analisis file.

**Request:** Multipart form data
- `file`: File to analyze
- `options`: JSON string with analysis options

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "analysis_id",
    "filename": "suspicious.php",
    "fileSize": 1024,
    "fileType": "text/php",
    "status": "analyzing",
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

### GET /file/analysis/{id}
Mendapatkan hasil analisis file.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "analysis_id",
    "filename": "suspicious.php",
    "status": "completed",
    "threatLevel": "malicious",
    "analysisResults": {
      "isWebshell": true,
      "malwareSignatures": ["PHP.Webshell.Generic"],
      "suspiciousFunctions": ["eval", "exec", "system"],
      "secrets": []
    },
    "completedAt": "2025-01-01T00:01:00Z"
  }
}
```

## CVE Intelligence Endpoints

### GET /cve/search
Pencarian CVE database.

**Query Parameters:**
- `q`: Search query
- `severity`: Filter by severity (low|medium|high|critical)
- `year`: Filter by year
- `page`: Page number
- `limit`: Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "cves": [
      {
        "id": "CVE-2024-1234",
        "description": "Remote code execution vulnerability",
        "severity": "critical",
        "cvssScore": 9.8,
        "publishedDate": "2024-01-01",
        "affectedProducts": ["Product A", "Product B"],
        "exploitAvailable": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### GET /cve/{id}
Mendapatkan detail CVE berdasarkan ID.

### GET /cve/latest
Mendapatkan CVE terbaru.

## Bot Management Endpoints

### GET /bot/configurations
Mendapatkan konfigurasi bot (admin only).

### POST /bot/configurations
Membuat konfigurasi bot baru (super admin only).

### GET /bot/configurations/{id}/qr
Mendapatkan QR code untuk WhatsApp bot.

### POST /bot/configurations/{id}/connect
Menghubungkan bot.

## Plan & Subscription Endpoints

### GET /plans
Mendapatkan daftar plan yang tersedia.

### POST /plans/subscribe
Subscribe ke plan tertentu.

### GET /plans/subscription
Mendapatkan subscription aktif user.

### GET /plans/usage
Mendapatkan usage statistics user.

## Admin Endpoints

### GET /admin/users
Mendapatkan daftar user (admin only).

### GET /admin/stats
Mendapatkan statistik sistem (admin only).

### GET /admin/settings
Mendapatkan pengaturan sistem (admin only).

### PATCH /admin/settings
Update pengaturan sistem (super admin only).

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_CREDENTIALS` | Email atau password salah |
| `TOKEN_EXPIRED` | JWT token sudah expired |
| `INSUFFICIENT_PERMISSIONS` | User tidak memiliki permission |
| `RATE_LIMIT_EXCEEDED` | Rate limit terlampaui |
| `QUOTA_EXCEEDED` | Kuota plan terlampaui |
| `INVALID_INPUT` | Input tidak valid |
| `RESOURCE_NOT_FOUND` | Resource tidak ditemukan |
| `SCAN_IN_PROGRESS` | Scan sedang berjalan |
| `FILE_TOO_LARGE` | File terlalu besar |
| `UNSUPPORTED_FILE_TYPE` | Tipe file tidak didukung |

## SDKs & Libraries

### JavaScript/Node.js
```bash
npm install kodexguard-sdk
```

```javascript
import { KodeXGuard } from 'kodexguard-sdk';

const client = new KodeXGuard({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.kodexguard.com'
});

// OSINT Search
const result = await client.osint.search('nik', '1234567890123456');

// Vulnerability Scan
const scan = await client.scanner.scan('https://example.com');
```

### Python
```bash
pip install kodexguard-python
```

```python
from kodexguard import KodeXGuard

client = KodeXGuard(api_key='your-api-key')

# OSINT Search
result = client.osint.search('email', '<EMAIL>')

# File Analysis
with open('suspicious.php', 'rb') as f:
    analysis = client.file.analyze(f)
```

## Webhooks

KodeXGuard mendukung webhooks untuk notifikasi real-time:

### Scan Completed
```json
{
  "event": "scan.completed",
  "data": {
    "scanId": "scan_id",
    "status": "completed",
    "vulnerabilities": 5,
    "riskScore": 8.5
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### File Analysis Completed
```json
{
  "event": "file.analysis.completed",
  "data": {
    "analysisId": "analysis_id",
    "threatLevel": "malicious",
    "detectedThreats": ["webshell", "backdoor"]
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## Support

Untuk bantuan API:
- **Documentation**: [docs.kodexguard.com/api](https://docs.kodexguard.com/api)
- **Support**: <EMAIL>
- **Status**: [status.kodexguard.com](https://status.kodexguard.com)
