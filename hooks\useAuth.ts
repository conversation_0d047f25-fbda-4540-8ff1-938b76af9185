'use client';

import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { authApi, setAuthToken, removeAuthToken } from '@/lib/api';
import { User, LoginRequest, RegisterRequest, ChangePasswordRequest } from '@/types/user';

// Auth hooks
export function useAuth() {
  const queryClient = useQueryClient();
  const router = useRouter();

  // Get current user
  const {
    data: user,
    isLoading,
    error,
    refetch,
  } = useQuery<User>({
    queryKey: ['auth', 'user'],
    queryFn: authApi.getProfile,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: ({ email, password }: LoginRequest) => authApi.login(email, password),
    onSuccess: (data: any) => {
      setAuthToken(data.token);
      if (data.refreshToken) {
        localStorage.setItem('refresh_token', data.refreshToken);
      }
      queryClient.setQueryData(['auth', 'user'], data.user);
      toast.success('Login berhasil!');
      router.push('/dashboard/panel-user');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Login gagal');
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: (userData: RegisterRequest) => authApi.register(userData),
    onSuccess: () => {
      toast.success('Registrasi berhasil! Silakan login.');
      router.push('/login');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Registrasi gagal');
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: authApi.logout,
    onSuccess: () => {
      removeAuthToken();
      queryClient.clear();
      toast.success('Logout berhasil');
      router.push('/');
    },
    onError: () => {
      // Force logout even if API call fails
      removeAuthToken();
      queryClient.clear();
      router.push('/');
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (data: Partial<User>) => authApi.updateProfile(data),
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(['auth', 'user'], updatedUser);
      toast.success('Profil berhasil diperbarui');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Gagal memperbarui profil');
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: ({ currentPassword, newPassword }: ChangePasswordRequest) =>
      authApi.changePassword(currentPassword, newPassword),
    onSuccess: () => {
      toast.success('Password berhasil diubah');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Gagal mengubah password');
    },
  });

  // Generate API key mutation
  const generateApiKeyMutation = useMutation({
    mutationFn: authApi.generateApiKey,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
      toast.success('API Key baru berhasil dibuat');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Gagal membuat API Key');
    },
  });

  return {
    // Data
    user,
    isLoading,
    error,
    isAuthenticated: !!user,

    // Actions
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    updateProfile: updateProfileMutation.mutate,
    changePassword: changePasswordMutation.mutate,
    generateApiKey: generateApiKeyMutation.mutate,
    refetch,

    // Loading states
    isLoggingIn: loginMutation.isPending,
    isRegistering: registerMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isUpdatingProfile: updateProfileMutation.isPending,
    isChangingPassword: changePasswordMutation.isPending,
    isGeneratingApiKey: generateApiKeyMutation.isPending,
  };
}

// Permission hooks
export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Import role permissions
    const { ROLE_PERMISSIONS } = require('@/constants/roles');
    const permissions = ROLE_PERMISSIONS[user.role] || [];
    return permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  const canAccessRole = (targetRole: string): boolean => {
    if (!user) return false;
    
    const { ROLE_HIERARCHY } = require('@/constants/roles');
    return ROLE_HIERARCHY[user.role] >= ROLE_HIERARCHY[targetRole];
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessRole,
    isAdmin: user?.role === 'admin' || user?.role === 'super_admin',
    isSuperAdmin: user?.role === 'super_admin',
  };
}

// Plan hooks
export function usePlan() {
  const { user } = useAuth();

  const getPlanLimits = () => {
    if (!user) return null;
    
    const { PLAN_LIMITS } = require('@/constants/plans');
    return PLAN_LIMITS[user.plan] || null;
  };

  const canUseFeature = (feature: string): boolean => {
    const limits = getPlanLimits();
    if (!limits) return false;
    
    // Check specific feature limits
    switch (feature) {
      case 'customDorks':
        return limits.customDorks;
      case 'advancedFeatures':
        return limits.advancedFeatures;
      case 'priorityQueue':
        return limits.priorityQueue;
      case 'whiteLabel':
        return limits.whiteLabel;
      default:
        return true;
    }
  };

  const getRemainingQuota = (type: 'api' | 'scan' | 'file' | 'osint' | 'cve' | 'bot'): number => {
    const limits = getPlanLimits();
    if (!limits || !user) return 0;
    
    switch (type) {
      case 'api':
        return limits.apiCallsPerDay === -1 ? Infinity : Math.max(0, limits.apiCallsPerDay - user.api_usage);
      case 'scan':
        return limits.scanPerDay === -1 ? Infinity : Math.max(0, limits.scanPerDay - user.total_scans);
      case 'file':
        return limits.fileUploadPerDay === -1 ? Infinity : Math.max(0, limits.fileUploadPerDay - user.total_files);
      default:
        return Infinity;
    }
  };

  const isQuotaExceeded = (type: 'api' | 'scan' | 'file' | 'osint' | 'cve' | 'bot'): boolean => {
    const remaining = getRemainingQuota(type);
    return remaining <= 0 && remaining !== Infinity;
  };

  return {
    plan: user?.plan,
    planExpiry: user?.plan_expiry,
    limits: getPlanLimits(),
    canUseFeature,
    getRemainingQuota,
    isQuotaExceeded,
    isPremium: user?.plan !== 'free',
  };
}

// Auth guard hook
export function useAuthGuard(requiredPermissions?: string[], redirectTo = '/login') {
  const { user, isLoading } = useAuth();
  const { hasAllPermissions } = usePermissions();
  const router = useRouter();

  React.useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push(redirectTo);
        return;
      }

      if (requiredPermissions && !hasAllPermissions(requiredPermissions)) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [user, isLoading, requiredPermissions, hasAllPermissions, router, redirectTo]);

  return {
    user,
    isLoading,
    isAuthorized: !!user && (!requiredPermissions || hasAllPermissions(requiredPermissions)),
  };
}
