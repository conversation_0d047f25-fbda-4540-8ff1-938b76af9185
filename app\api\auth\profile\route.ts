import { NextRequest, NextResponse } from 'next/server';
import { getUserByToken, updateUser } from '@/lib/auth';
import { z } from 'zod';

// Validation schema for profile update
const updateProfileSchema = z.object({
  fullName: z.string().min(2).max(255).optional(),
  bio: z.string().max(500).optional(),
  avatar: z.string().url().optional(),
});

// Middleware to get user from token
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Token tidak ditemukan');
  }

  const token = authHeader.substring(7);
  const user = await getUserByToken(token);
  
  if (!user) {
    throw new Error('Token tidak valid');
  }

  return user;
}

// GET /api/auth/profile - Get user profile
export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);

    return NextResponse.json({
      success: true,
      data: user,
      message: 'Profil berhasil diambil'
    });

  } catch (error: any) {
    console.error('Get profile error:', error);

    return NextResponse.json({
      success: false,
      error: error.message || 'Gagal mengambil profil',
      code: 'UNAUTHORIZED'
    }, { status: 401 });
  }
}

// PATCH /api/auth/profile - Update user profile
export async function PATCH(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request);
    const body = await request.json();

    // Validate input
    const validatedData = updateProfileSchema.parse(body);

    // Update user profile
    const updatedUser = await updateUser(user.id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Profil berhasil diperbarui'
    });

  } catch (error: any) {
    console.error('Update profile error:', error);

    // Handle validation errors
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        error: 'Data tidak valid',
        errors: error.errors.reduce((acc: any, err: any) => {
          acc[err.path[0]] = [err.message];
          return acc;
        }, {})
      }, { status: 400 });
    }

    // Handle auth errors
    if (error.message.includes('Token')) {
      return NextResponse.json({
        success: false,
        error: error.message,
        code: 'UNAUTHORIZED'
      }, { status: 401 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Gagal memperbarui profil',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
