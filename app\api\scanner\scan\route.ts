import { NextRequest, NextResponse } from 'next/server';
import { getUserByToken, validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { executeQuery, indexDocument } from '@/lib/database';
import { z } from 'zod';
import { randomBytes } from 'crypto';

// Validation schema
const scanSchema = z.object({
  targetUrl: z.string().url('URL tidak valid'),
  scanType: z.enum(['quick', 'full', 'custom']).default('quick'),
  options: z.object({
    checkSQLi: z.boolean().optional().default(true),
    checkXSS: z.boolean().optional().default(true),
    checkLFI: z.boolean().optional().default(true),
    checkRCE: z.boolean().optional().default(true),
    checkCSRF: z.boolean().optional().default(true),
    checkPathTraversal: z.boolean().optional().default(true),
    maxDepth: z.number().min(1).max(10).optional().default(3),
    timeout: z.number().min(30).max(3600).optional().default(300),
    userAgent: z.string().optional(),
    headers: z.record(z.string()).optional(),
  }).optional().default({}),
});

// Get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const user = await getUserByToken(token);
    if (user) return user;
  }

  const apiKey = request.headers.get('x-api-key');
  if (apiKey) {
    const user = await validateApiKey(apiKey);
    if (user) return user;
  }

  throw new Error('Authentication required');
}

// Mock vulnerability scanner function
async function performVulnerabilityScanning(targetUrl: string, scanType: string, options: any) {
  // Simulate scanning time based on scan type
  const scanDuration = {
    quick: 2000,
    full: 5000,
    custom: 3000
  };
  
  await new Promise(resolve => setTimeout(resolve, scanDuration[scanType as keyof typeof scanDuration]));

  // Mock vulnerabilities found
  const mockVulnerabilities = [
    {
      id: 'vuln_001',
      type: 'SQL Injection',
      severity: 'high',
      cvssScore: 8.5,
      cveId: 'CVE-2024-1234',
      url: `${targetUrl}/login.php`,
      parameter: 'username',
      payload: "' OR '1'='1",
      description: 'SQL injection vulnerability in login form',
      impact: 'Attacker can bypass authentication and access sensitive data',
      recommendation: 'Use parameterized queries and input validation',
      evidence: {
        request: "POST /login.php\nusername=' OR '1'='1&password=test",
        response: "Welcome admin! You are now logged in.",
      }
    },
    {
      id: 'vuln_002',
      type: 'Cross-Site Scripting (XSS)',
      severity: 'medium',
      cvssScore: 6.1,
      cveId: null,
      url: `${targetUrl}/search.php`,
      parameter: 'q',
      payload: '<script>alert("XSS")</script>',
      description: 'Reflected XSS vulnerability in search functionality',
      impact: 'Attacker can execute malicious scripts in user browsers',
      recommendation: 'Implement proper input sanitization and output encoding',
      evidence: {
        request: "GET /search.php?q=<script>alert('XSS')</script>",
        response: "Search results for: <script>alert('XSS')</script>",
      }
    },
    {
      id: 'vuln_003',
      type: 'Local File Inclusion (LFI)',
      severity: 'high',
      cvssScore: 7.8,
      cveId: null,
      url: `${targetUrl}/page.php`,
      parameter: 'file',
      payload: '../../../../etc/passwd',
      description: 'Local file inclusion vulnerability allows reading system files',
      impact: 'Attacker can read sensitive system files and configuration',
      recommendation: 'Validate and sanitize file path parameters',
      evidence: {
        request: "GET /page.php?file=../../../../etc/passwd",
        response: "root:x:0:0:root:/root:/bin/bash\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin",
      }
    }
  ];

  // Filter vulnerabilities based on scan options
  const filteredVulns = mockVulnerabilities.filter(vuln => {
    if (vuln.type === 'SQL Injection' && !options.checkSQLi) return false;
    if (vuln.type.includes('XSS') && !options.checkXSS) return false;
    if (vuln.type.includes('LFI') && !options.checkLFI) return false;
    return true;
  });

  // Calculate risk score
  const riskScore = filteredVulns.reduce((total, vuln) => {
    const severityScores = { low: 1, medium: 3, high: 7, critical: 10 };
    return total + (severityScores[vuln.severity as keyof typeof severityScores] || 0);
  }, 0);

  return {
    vulnerabilities: filteredVulns,
    summary: {
      total: filteredVulns.length,
      critical: filteredVulns.filter(v => v.severity === 'critical').length,
      high: filteredVulns.filter(v => v.severity === 'high').length,
      medium: filteredVulns.filter(v => v.severity === 'medium').length,
      low: filteredVulns.filter(v => v.severity === 'low').length,
    },
    riskScore,
    scanDuration: scanDuration[scanType as keyof typeof scanDuration] / 1000,
  };
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getAuthenticatedUser(request);

    // Parse and validate request body
    const body = await request.json();
    const validatedData = scanSchema.parse(body);
    const { targetUrl, scanType, options } = validatedData;

    // Check user plan limits
    const { PLAN_LIMITS } = require('@/constants/plans');
    const userLimits = PLAN_LIMITS[user.plan];
    
    if (userLimits && userLimits.scanPerDay !== -1) {
      // Check daily usage (simplified)
      const dailyUsage = 0; // Get from database
      if (dailyUsage >= userLimits.scanPerDay) {
        return NextResponse.json({
          success: false,
          error: 'Kuota scan harian telah habis',
          code: 'QUOTA_EXCEEDED'
        }, { status: 429 });
      }
    }

    // Generate scan ID
    const scanId = randomBytes(16).toString('hex');

    // Create scan record
    await executeQuery(
      `INSERT INTO vulnerability_scans (id, user_id, target_url, scan_type, status)
       VALUES (?, ?, ?, ?, 'running')`,
      [scanId, user.id, targetUrl, scanType]
    );

    // Perform vulnerability scanning
    const scanResults = await performVulnerabilityScanning(targetUrl, scanType, options);

    // Calculate CVSS score (average of all vulnerabilities)
    const avgCvssScore = scanResults.vulnerabilities.length > 0
      ? scanResults.vulnerabilities.reduce((sum, v) => sum + v.cvssScore, 0) / scanResults.vulnerabilities.length
      : 0;

    // Update scan record with results
    await executeQuery(
      `UPDATE vulnerability_scans 
       SET status = 'completed', 
           vulnerabilities = ?, 
           risk_score = ?,
           cvss_score = ?,
           scan_duration = ?,
           completed_at = NOW()
       WHERE id = ?`,
      [
        JSON.stringify(scanResults.vulnerabilities),
        scanResults.riskScore,
        avgCvssScore,
        scanResults.scanDuration,
        scanId
      ]
    );

    // Index results in Elasticsearch
    try {
      await indexDocument('scan_results', scanId, {
        userId: user.id,
        targetUrl,
        scanType,
        vulnerabilities: scanResults.vulnerabilities,
        riskScore: scanResults.riskScore,
        timestamp: new Date().toISOString(),
      });
    } catch (esError) {
      console.error('Elasticsearch indexing error:', esError);
    }

    // Update user stats
    await executeQuery(
      'UPDATE users SET total_scans = total_scans + 1, api_usage = api_usage + 1 WHERE id = ?',
      [user.id]
    );

    return NextResponse.json({
      success: true,
      data: {
        id: scanId,
        targetUrl,
        scanType,
        status: 'completed',
        vulnerabilities: scanResults.vulnerabilities,
        summary: scanResults.summary,
        riskScore: scanResults.riskScore,
        cvssScore: avgCvssScore,
        scanDuration: scanResults.scanDuration,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
      },
      message: `Scan selesai. Ditemukan ${scanResults.vulnerabilities.length} kerentanan.`
    });

  } catch (error: any) {
    console.error('Vulnerability scan error:', error);

    // Handle validation errors
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        error: 'Data tidak valid',
        errors: error.errors.reduce((acc: any, err: any) => {
          acc[err.path[0]] = [err.message];
          return acc;
        }, {})
      }, { status: 400 });
    }

    // Handle auth errors
    if (error.message.includes('Authentication')) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      }, { status: 401 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Gagal melakukan vulnerability scan',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
