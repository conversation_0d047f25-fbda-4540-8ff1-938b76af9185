'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  Shield, 
  Scan, 
  ArrowLeft,
  Loader2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Bug,
  Zap,
  Eye,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function VulnerabilityScanner() {
  const [targetUrl, setTargetUrl] = useState('');
  const [scanType, setScanType] = useState('quick');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<any>(null);
  const [scanOptions, setScanOptions] = useState({
    checkSQLi: true,
    checkXSS: true,
    checkLFI: true,
    checkRCE: true,
    checkCSRF: true,
    checkPathTraversal: true,
    maxDepth: 3,
    timeout: 300,
  });

  const scanTypes = [
    { 
      id: 'quick', 
      label: 'Quick Scan', 
      description: 'Scan cepat untuk kerentanan umum',
      duration: '~2 menit',
      icon: <Zap className="h-4 w-4" />
    },
    { 
      id: 'full', 
      label: 'Full Scan', 
      description: 'Scan lengkap semua kerentanan',
      duration: '~5-10 menit',
      icon: <Target className="h-4 w-4" />
    },
    { 
      id: 'custom', 
      label: 'Custom Scan', 
      description: 'Scan dengan pengaturan khusus',
      duration: 'Bervariasi',
      icon: <Settings className="h-4 w-4" />
    },
  ];

  const handleScan = async () => {
    if (!targetUrl.trim()) return;

    setIsScanning(true);
    
    try {
      // Simulate API call
      const response = await fetch('/api/scanner/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          targetUrl,
          scanType,
          options: scanOptions,
        }),
      });

      if (!response.ok) {
        throw new Error('Scan failed');
      }

      const result = await response.json();
      setScanResults(result.data);
    } catch (error) {
      console.error('Scan error:', error);
      // For demo, use mock data
      setTimeout(() => {
        const mockResults = generateMockResults(targetUrl, scanType);
        setScanResults(mockResults);
      }, 3000);
    } finally {
      setIsScanning(false);
    }
  };

  const generateMockResults = (url: string, type: string) => {
    const vulnerabilities = [
      {
        id: 'vuln_001',
        type: 'SQL Injection',
        severity: 'high',
        cvssScore: 8.5,
        url: `${url}/login.php`,
        parameter: 'username',
        description: 'SQL injection vulnerability in login form',
        recommendation: 'Use parameterized queries and input validation',
      },
      {
        id: 'vuln_002',
        type: 'Cross-Site Scripting (XSS)',
        severity: 'medium',
        cvssScore: 6.1,
        url: `${url}/search.php`,
        parameter: 'q',
        description: 'Reflected XSS vulnerability in search functionality',
        recommendation: 'Implement proper input sanitization and output encoding',
      },
      {
        id: 'vuln_003',
        type: 'Local File Inclusion (LFI)',
        severity: 'high',
        cvssScore: 7.8,
        url: `${url}/page.php`,
        parameter: 'file',
        description: 'Local file inclusion vulnerability allows reading system files',
        recommendation: 'Validate and sanitize file path parameters',
      }
    ];

    return {
      id: 'scan_' + Date.now(),
      targetUrl: url,
      scanType: type,
      status: 'completed',
      vulnerabilities,
      summary: {
        total: vulnerabilities.length,
        critical: 0,
        high: 2,
        medium: 1,
        low: 0,
      },
      riskScore: 15,
      cvssScore: 7.5,
      scanDuration: type === 'quick' ? 2 : type === 'full' ? 5 : 3,
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
    };
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getRiskLevel = (score: number) => {
    if (score >= 20) return { level: 'Critical', color: 'text-red-600' };
    if (score >= 15) return { level: 'High', color: 'text-orange-600' };
    if (score >= 10) return { level: 'Medium', color: 'text-yellow-600' };
    if (score >= 5) return { level: 'Low', color: 'text-green-600' };
    return { level: 'Minimal', color: 'text-blue-600' };
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-gray-200 dark:border-gray-700 bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/dashboard/panel-user">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <Scan className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">Vulnerability Scanner</h1>
                <p className="text-muted-foreground">Deteksi kerentanan keamanan website</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/scanner/history">Riwayat Scan</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Scan Form */}
          <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <h2 className="text-xl font-semibold mb-6">Konfigurasi Scan</h2>

            {/* Target URL */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Target URL</label>
              <input
                type="url"
                value={targetUrl}
                onChange={(e) => setTargetUrl(e.target.value)}
                placeholder="https://example.com"
                className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-background focus:border-primary focus:outline-none"
              />
            </div>

            {/* Scan Type */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-3">Jenis Scan</label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {scanTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setScanType(type.id)}
                    className={`p-4 rounded-lg border transition-all text-left ${
                      scanType === type.id
                        ? 'border-primary bg-primary/10 text-primary'
                        : 'border-gray-200 dark:border-gray-700 hover:border-primary/40'
                    }`}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      {type.icon}
                      <span className="font-medium">{type.label}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">{type.description}</p>
                    <p className="text-xs text-muted-foreground">{type.duration}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Scan Options (for custom scan) */}
            {scanType === 'custom' && (
              <div className="mb-6">
                <label className="block text-sm font-medium mb-3">Opsi Scan</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Object.entries(scanOptions).map(([key, value]) => {
                    if (typeof value === 'boolean') {
                      return (
                        <label key={key} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setScanOptions(prev => ({
                              ...prev,
                              [key]: e.target.checked
                            }))}
                            className="rounded border-gray-200 dark:border-gray-700"
                          />
                          <span className="text-sm">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                        </label>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            )}

            {/* Scan Button */}
            <Button 
              onClick={handleScan}
              disabled={!targetUrl.trim() || isScanning}
              className="w-full md:w-auto px-8"
              variant="cyber"
            >
              {isScanning ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Scanning...
                </>
              ) : (
                <>
                  <Scan className="h-4 w-4 mr-2" />
                  Mulai Scan
                </>
              )}
            </Button>
          </div>

          {/* Scan Results */}
          {scanResults && (
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Hasil Scan</h2>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-green-500">Scan Selesai</span>
                </div>
              </div>

              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-primary">{scanResults.summary.total}</div>
                  <div className="text-sm text-muted-foreground">Total Kerentanan</div>
                </div>
                <div className="p-4 bg-secondary/20 rounded-lg">
                  <div className={`text-2xl font-bold ${getRiskLevel(scanResults.riskScore).color}`}>
                    {getRiskLevel(scanResults.riskScore).level}
                  </div>
                  <div className="text-sm text-muted-foreground">Risk Level</div>
                </div>
                <div className="p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-orange-500">{scanResults.cvssScore}</div>
                  <div className="text-sm text-muted-foreground">CVSS Score</div>
                </div>
                <div className="p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-500">{scanResults.scanDuration}s</div>
                  <div className="text-sm text-muted-foreground">Durasi Scan</div>
                </div>
              </div>

              {/* Vulnerabilities */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Kerentanan Ditemukan</h3>
                {scanResults.vulnerabilities.length > 0 ? (
                  <div className="space-y-4">
                    {scanResults.vulnerabilities.map((vuln: any) => (
                      <div key={vuln.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-semibold text-lg">{vuln.type}</h4>
                            <p className="text-muted-foreground">{vuln.url}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(vuln.severity)}`}>
                              {vuln.severity.toUpperCase()}
                            </span>
                            <span className="text-sm text-muted-foreground">CVSS: {vuln.cvssScore}</span>
                          </div>
                        </div>
                        <p className="text-sm mb-2">{vuln.description}</p>
                        <div className="text-xs text-muted-foreground">
                          <strong>Parameter:</strong> {vuln.parameter}
                        </div>
                        <div className="text-xs text-green-600 mt-2">
                          <strong>Rekomendasi:</strong> {vuln.recommendation}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <p className="text-muted-foreground">Tidak ada kerentanan ditemukan</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
