import Link from "next/link";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, File<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Lock } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Header */}
      <header className="border-b border-border/40 backdrop-blur-sm bg-background/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold neon-text">KodeXGuard</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/osint" className="text-muted-foreground hover:text-primary transition-colors">
              OSINT
            </Link>
            <Link href="/scanner" className="text-muted-foreground hover:text-primary transition-colors">
              <PERSON><PERSON><PERSON>
            </Link>
            <Link href="/cve" className="text-muted-foreground hover:text-primary transition-colors">
              CVE
            </Link>
            <Link href="/plan" className="text-muted-foreground hover:text-primary transition-colors">
              Pricing
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard/panel-user"
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              Dashboard
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 cyber-gradient bg-clip-text text-transparent">
            Platform Cybersecurity
            <br />
            <span className="neon-text">& Bug Hunting</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Platform mandiri untuk investigasi OSINT, vulnerability scanning, file analysis,
            CVE intelligence, dan bug hunting dengan dukungan bot automation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/dashboard/panel-user"
              className="px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all cyber-glow"
            >
              Mulai Sekarang
            </Link>
            <Link
              href="/plan"
              className="px-8 py-4 border border-primary text-primary rounded-lg hover:bg-primary/10 transition-colors"
            >
              Lihat Pricing
            </Link>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 px-4 bg-secondary/20">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Fitur Unggulan</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FeatureCard
              icon={<Search className="h-8 w-8" />}
              title="OSINT Investigation"
              description="Investigasi mendalam dengan pencarian NIK, NPWP, IMEI, dan data leak"
              href="/osint"
            />
            <FeatureCard
              icon={<Scan className="h-8 w-8" />}
              title="Vulnerability Scanner"
              description="Deteksi kerentanan SQLi, XSS, LFI, RCE dengan mapping CVE"
              href="/scanner"
            />
            <FeatureCard
              icon={<FileText className="h-8 w-8" />}
              title="File Analyzer"
              description="Analisis malware, webshell, dan deteksi secret/token"
              href="/file-analyzer"
            />
            <FeatureCard
              icon={<Bot className="h-8 w-8" />}
              title="Bot Automation"
              description="Integrasi WhatsApp & Telegram untuk scanning otomatis"
              href="/bot"
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <StatCard number="10K+" label="Scans Completed" />
            <StatCard number="500+" label="CVE Database" />
            <StatCard number="1K+" label="Active Users" />
            <StatCard number="99.9%" label="Uptime" />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/40 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Shield className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">KodeXGuard</span>
              </div>
              <p className="text-muted-foreground">
                Platform cybersecurity terdepan untuk profesional keamanan.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/osint" className="hover:text-primary">OSINT</Link></li>
                <li><Link href="/scanner" className="hover:text-primary">Scanner</Link></li>
                <li><Link href="/cve" className="hover:text-primary">CVE Intelligence</Link></li>
                <li><Link href="/playground" className="hover:text-primary">API Playground</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Community</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/leaderboard" className="hover:text-primary">Leaderboard</Link></li>
                <li><Link href="/plan" className="hover:text-primary">Pricing</Link></li>
                <li><Link href="/docs" className="hover:text-primary">Documentation</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/contact" className="hover:text-primary">Contact</Link></li>
                <li><Link href="/help" className="hover:text-primary">Help Center</Link></li>
                <li><Link href="/status" className="hover:text-primary">Status</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/40 mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2025 KodeXGuard. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

function FeatureCard({ icon, title, description, href }: {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}) {
  return (
    <Link href={href} className="group">
      <div className="p-6 rounded-lg border border-border/40 hover:border-primary/40 transition-all hover:cyber-glow-blue bg-card">
        <div className="text-primary mb-4 group-hover:scale-110 transition-transform">
          {icon}
        </div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </Link>
  );
}

function StatCard({ number, label }: { number: string; label: string }) {
  return (
    <div className="p-6">
      <div className="text-4xl font-bold text-primary mb-2 neon-text">{number}</div>
      <div className="text-muted-foreground">{label}</div>
    </div>
  );
}
