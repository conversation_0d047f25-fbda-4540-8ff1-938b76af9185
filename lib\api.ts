import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public errors?: Record<string, string[]>
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add auth token if available
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      // Add API key if available
      const apiKey = process.env.NEXT_PUBLIC_API_KEY;
      if (apiKey) {
        config.headers['X-API-Key'] = apiKey;
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      return response;
    },
    (error) => {
      const message = error.response?.data?.message || error.message || 'An error occurred';
      const status = error.response?.status;
      const code = error.response?.data?.code;
      const errors = error.response?.data?.errors;

      // Handle authentication errors
      if (status === 401) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }

      // Show error toast for client-side errors
      if (typeof window !== 'undefined' && status >= 400) {
        toast.error(message);
      }

      return Promise.reject(new ApiError(message, status, code, errors));
    }
  );

  return client;
};

// API client instance
export const apiClient = createApiClient();

// Generic API methods
export const api = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },

  // Upload file
  upload: async <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data.data as T;
  },

  // Download file
  download: async (url: string, filename?: string): Promise<void> => {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

// Authentication API
export const authApi = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),

  register: (userData: {
    username: string;
    email: string;
    password: string;
    fullName: string;
  }) => api.post('/auth/register', userData),

  logout: () => api.post('/auth/logout'),

  refreshToken: (refreshToken: string) =>
    api.post('/auth/refresh', { refreshToken }),

  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),

  resetPassword: (token: string, password: string) =>
    api.post('/auth/reset-password', { token, password }),

  verifyEmail: (token: string) =>
    api.post('/auth/verify-email', { token }),

  changePassword: (currentPassword: string, newPassword: string) =>
    api.post('/auth/change-password', { currentPassword, newPassword }),

  getProfile: () => api.get('/auth/profile'),

  updateProfile: (data: any) => api.patch('/auth/profile', data),

  generateApiKey: () => api.post('/auth/api-key/generate'),

  getApiKey: () => api.get('/auth/api-key'),
};

// OSINT API
export const osintApi = {
  search: (type: string, query: string, options?: any) =>
    api.post('/osint/search', { type, query, options }),

  getSearchHistory: (page = 1, limit = 20) =>
    api.get(`/osint/history?page=${page}&limit=${limit}`),

  getSearchResult: (searchId: string) =>
    api.get(`/osint/search/${searchId}`),

  deleteSearch: (searchId: string) =>
    api.delete(`/osint/search/${searchId}`),
};

// Scanner API
export const scannerApi = {
  startScan: (targetUrl: string, scanType: string, options?: any) =>
    api.post('/scanner/scan', { targetUrl, scanType, options }),

  getScanHistory: (page = 1, limit = 20) =>
    api.get(`/scanner/history?page=${page}&limit=${limit}`),

  getScanResult: (scanId: string) =>
    api.get(`/scanner/scan/${scanId}`),

  cancelScan: (scanId: string) =>
    api.post(`/scanner/scan/${scanId}/cancel`),

  deleteScan: (scanId: string) =>
    api.delete(`/scanner/scan/${scanId}`),
};

// File Analysis API
export const fileApi = {
  analyzeFile: (file: File, onProgress?: (progress: number) => void) =>
    api.upload('/file/analyze', file, onProgress),

  getAnalysisHistory: (page = 1, limit = 20) =>
    api.get(`/file/history?page=${page}&limit=${limit}`),

  getAnalysisResult: (analysisId: string) =>
    api.get(`/file/analysis/${analysisId}`),

  deleteAnalysis: (analysisId: string) =>
    api.delete(`/file/analysis/${analysisId}`),
};

// CVE API
export const cveApi = {
  search: (query: string, filters?: any, page = 1, limit = 20) =>
    api.get(`/cve/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`, {
      params: filters,
    }),

  getCve: (cveId: string) =>
    api.get(`/cve/${cveId}`),

  getLatest: (limit = 10) =>
    api.get(`/cve/latest?limit=${limit}`),

  getStats: () =>
    api.get('/cve/stats'),
};

// Bot API
export const botApi = {
  getConfigurations: () =>
    api.get('/bot/configurations'),

  createConfiguration: (data: any) =>
    api.post('/bot/configurations', data),

  updateConfiguration: (id: string, data: any) =>
    api.patch(`/bot/configurations/${id}`, data),

  deleteConfiguration: (id: string) =>
    api.delete(`/bot/configurations/${id}`),

  connectBot: (id: string) =>
    api.post(`/bot/configurations/${id}/connect`),

  disconnectBot: (id: string) =>
    api.post(`/bot/configurations/${id}/disconnect`),

  getQrCode: (id: string) =>
    api.get(`/bot/configurations/${id}/qr`),
};

// Plan API
export const planApi = {
  getPlans: () => api.get('/plans'),

  getPlan: (id: string) => api.get(`/plans/${id}`),

  subscribe: (planId: string, paymentMethod: string) =>
    api.post('/plans/subscribe', { planId, paymentMethod }),

  getSubscription: () => api.get('/plans/subscription'),

  cancelSubscription: () => api.post('/plans/subscription/cancel'),

  getUsage: () => api.get('/plans/usage'),
};

// Admin API
export const adminApi = {
  getUsers: (page = 1, limit = 20, filters?: any) =>
    api.get(`/admin/users?page=${page}&limit=${limit}`, { params: filters }),

  getUser: (id: string) => api.get(`/admin/users/${id}`),

  updateUser: (id: string, data: any) => api.patch(`/admin/users/${id}`, data),

  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),

  getSystemStats: () => api.get('/admin/stats'),

  getSystemSettings: () => api.get('/admin/settings'),

  updateSystemSettings: (settings: any) => api.patch('/admin/settings', settings),

  getAuditLogs: (page = 1, limit = 20) =>
    api.get(`/admin/audit-logs?page=${page}&limit=${limit}`),
};

// Utility functions
export const setAuthToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token);
  }
};

export const removeAuthToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  }
};

export const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token');
  }
  return null;
};
