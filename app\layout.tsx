import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "react-hot-toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "KodeXGuard - Platform Cybersecurity & Bug Hunting",
    template: "%s | KodeXGuard"
  },
  description: "Platform mandiri untuk investigasi OSINT, vulnerability scanning, file analysis, CVE intelligence, dan bug hunting dengan dukungan bot automation.",
  keywords: [
    "cybersecurity",
    "osint",
    "vulnerability scanner",
    "bug hunting",
    "penetration testing",
    "security research",
    "cve intelligence",
    "malware analysis",
    "ethical hacking"
  ],
  authors: [{ name: "KodeXGuard Team" }],
  creator: "KodeXGuard",
  publisher: "KodeXGuard",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "id_ID",
    url: "https://kodexguard.com",
    title: "KodeXGuard - Platform Cybersecurity & Bug Hunting",
    description: "Platform mandiri untuk investigasi OSINT, vulnerability scanning, dan bug hunting",
    siteName: "KodeXGuard",
  },
  twitter: {
    card: "summary_large_image",
    title: "KodeXGuard - Platform Cybersecurity & Bug Hunting",
    description: "Platform mandiri untuk investigasi OSINT, vulnerability scanning, dan bug hunting",
    creator: "@kodexguard",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
