'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Search, 
  Scan, 
  FileText, 
  Bot, 
  Users, 
  Zap, 
  Lock,
  Activity,
  TrendingUp,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function UserDashboard() {
  // Mock data - in real app, this would come from API
  const userStats = {
    totalScans: 156,
    totalFiles: 89,
    apiUsage: 2340,
    score: 1250,
    plan: 'Hobby',
    planExpiry: '2025-08-18',
  };

  const recentActivities = [
    {
      id: 1,
      type: 'scan',
      description: 'Vulnerability scan completed for example.com',
      timestamp: '2 menit yang lalu',
      status: 'success'
    },
    {
      id: 2,
      type: 'osint',
      description: 'OSINT search for email investigation',
      timestamp: '15 menit yang lalu',
      status: 'success'
    },
    {
      id: 3,
      type: 'file',
      description: 'File analysis completed - malware detected',
      timestamp: '1 jam yang lalu',
      status: 'warning'
    },
    {
      id: 4,
      type: 'api',
      description: 'API key regenerated',
      timestamp: '3 jam yang lalu',
      status: 'info'
    }
  ];

  const quickActions = [
    {
      title: 'OSINT Investigation',
      description: 'Investigasi mendalam data personal',
      icon: <Search className="h-6 w-6" />,
      href: '/osint',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Vulnerability Scanner',
      description: 'Scan kerentanan website',
      icon: <Scan className="h-6 w-6" />,
      href: '/scanner',
      color: 'from-red-500 to-pink-500'
    },
    {
      title: 'File Analyzer',
      description: 'Analisis file berbahaya',
      icon: <FileText className="h-6 w-6" />,
      href: '/file-analyzer',
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: 'CVE Intelligence',
      description: 'Database CVE terbaru',
      icon: <AlertTriangle className="h-6 w-6" />,
      href: '/cve',
      color: 'from-orange-500 to-yellow-500'
    },
    {
      title: 'Bot Automation',
      description: 'WhatsApp & Telegram bot',
      icon: <Bot className="h-6 w-6" />,
      href: '/bot',
      color: 'from-purple-500 to-indigo-500'
    },
    {
      title: 'API Playground',
      description: 'Test API endpoints',
      icon: <Zap className="h-6 w-6" />,
      href: '/playground',
      color: 'from-teal-500 to-blue-500'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/40 bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Shield className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">Dashboard</h1>
                <p className="text-muted-foreground">Selamat datang kembali!</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/profile">Profil</Link>
              </Button>
              <Button variant="cyber" asChild>
                <Link href="/plan">Upgrade Plan</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Scans"
            value={userStats.totalScans}
            icon={<Scan className="h-5 w-5" />}
            trend="+12%"
            color="text-blue-500"
          />
          <StatsCard
            title="Files Analyzed"
            value={userStats.totalFiles}
            icon={<FileText className="h-5 w-5" />}
            trend="+8%"
            color="text-green-500"
          />
          <StatsCard
            title="API Usage"
            value={userStats.apiUsage}
            icon={<Activity className="h-5 w-5" />}
            trend="+25%"
            color="text-purple-500"
          />
          <StatsCard
            title="Security Score"
            value={userStats.score}
            icon={<TrendingUp className="h-5 w-5" />}
            trend="+15%"
            color="text-orange-500"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <div className="bg-card rounded-lg border border-border/40 p-6">
              <h2 className="text-xl font-semibold mb-6">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <QuickActionCard key={index} {...action} />
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Plan Info */}
            <div className="bg-card rounded-lg border border-border/40 p-6">
              <h3 className="text-lg font-semibold mb-4">Plan Aktif</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Plan</span>
                  <span className="font-medium text-primary">{userStats.plan}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Berakhir</span>
                  <span className="font-medium">{userStats.planExpiry}</span>
                </div>
                <Button className="w-full" variant="outline" asChild>
                  <Link href="/plan">Kelola Plan</Link>
                </Button>
              </div>
            </div>

            {/* Recent Activities */}
            <div className="bg-card rounded-lg border border-border/40 p-6">
              <h3 className="text-lg font-semibold mb-4">Aktivitas Terbaru</h3>
              <div className="space-y-3">
                {recentActivities.map((activity) => (
                  <ActivityItem key={activity.id} {...activity} />
                ))}
              </div>
              <Button variant="ghost" className="w-full mt-4" asChild>
                <Link href="/activity">Lihat Semua</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function StatsCard({ title, value, icon, trend, color }: {
  title: string;
  value: number;
  icon: React.ReactNode;
  trend: string;
  color: string;
}) {
  return (
    <div className="bg-card rounded-lg border border-border/40 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground text-sm">{title}</p>
          <p className="text-2xl font-bold">{value.toLocaleString()}</p>
        </div>
        <div className={`${color}`}>
          {icon}
        </div>
      </div>
      <div className="flex items-center mt-2">
        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
        <span className="text-green-500 text-sm">{trend}</span>
        <span className="text-muted-foreground text-sm ml-1">dari bulan lalu</span>
      </div>
    </div>
  );
}

function QuickActionCard({ title, description, icon, href, color }: {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}) {
  return (
    <Link href={href} className="group">
      <div className="bg-card rounded-lg border border-border/40 p-4 hover:border-primary/40 transition-all hover:cyber-glow-blue">
        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${color} flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform`}>
          {icon}
        </div>
        <h3 className="font-semibold mb-1">{title}</h3>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
    </Link>
  );
}

function ActivityItem({ type, description, timestamp, status }: {
  type: string;
  description: string;
  timestamp: string;
  status: string;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'scan': return <Scan className="h-4 w-4" />;
      case 'osint': return <Search className="h-4 w-4" />;
      case 'file': return <FileText className="h-4 w-4" />;
      case 'api': return <Zap className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="flex items-start space-x-3">
      <div className={`${getStatusColor(status)} mt-1`}>
        {getTypeIcon(type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{description}</p>
        <div className="flex items-center text-xs text-muted-foreground">
          <Clock className="h-3 w-3 mr-1" />
          {timestamp}
        </div>
      </div>
    </div>
  );
}
