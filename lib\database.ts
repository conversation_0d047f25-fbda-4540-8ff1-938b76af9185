import mysql from 'mysql2/promise';
import { createClient } from 'redis';
import { Client } from '@elastic/elasticsearch';

// MySQL Connection
let mysqlConnection: mysql.Connection | null = null;

export async function getMySQLConnection(): Promise<mysql.Connection> {
  if (!mysqlConnection) {
    mysqlConnection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'kodexguard',
      charset: 'utf8mb4',
      timezone: '+00:00',
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: true,
    });
  }
  return mysqlConnection;
}

// Redis Connection
let redisClient: ReturnType<typeof createClient> | null = null;

export async function getRedisClient() {
  if (!redisClient) {
    redisClient = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      password: process.env.REDIS_PASSWORD || undefined,
    });

    redisClient.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('Redis Client Connected');
    });

    await redisClient.connect();
  }
  return redisClient;
}

// Elasticsearch Connection
let elasticsearchClient: Client | null = null;

export function getElasticsearchClient(): Client {
  if (!elasticsearchClient) {
    elasticsearchClient = new Client({
      node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
      auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD,
      } : undefined,
      requestTimeout: 30000,
      pingTimeout: 3000,
      sniffOnStart: false,
    });
  }
  return elasticsearchClient;
}

// Database utilities
export class DatabaseError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  try {
    const connection = await getMySQLConnection();
    const [rows] = await connection.execute(query, params);
    return rows as T[];
  } catch (error: any) {
    console.error('Database query error:', error);
    throw new DatabaseError(error.message, error.code);
  }
}

export async function executeTransaction<T>(
  callback: (connection: mysql.Connection) => Promise<T>
): Promise<T> {
  const connection = await getMySQLConnection();
  
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  }
}

// Cache utilities
export async function cacheSet(
  key: string,
  value: any,
  ttl: number = 3600
): Promise<void> {
  try {
    const redis = await getRedisClient();
    await redis.setEx(key, ttl, JSON.stringify(value));
  } catch (error) {
    console.error('Cache set error:', error);
  }
}

export async function cacheGet<T = any>(key: string): Promise<T | null> {
  try {
    const redis = await getRedisClient();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('Cache get error:', error);
    return null;
  }
}

export async function cacheDelete(key: string): Promise<void> {
  try {
    const redis = await getRedisClient();
    await redis.del(key);
  } catch (error) {
    console.error('Cache delete error:', error);
  }
}

export async function cacheDeletePattern(pattern: string): Promise<void> {
  try {
    const redis = await getRedisClient();
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(keys);
    }
  } catch (error) {
    console.error('Cache delete pattern error:', error);
  }
}

// Search utilities
export async function searchIndex(
  index: string,
  query: any,
  options: any = {}
): Promise<any> {
  try {
    const client = getElasticsearchClient();
    const response = await client.search({
      index,
      body: query,
      ...options,
    });
    return response.body;
  } catch (error) {
    console.error('Elasticsearch search error:', error);
    throw error;
  }
}

export async function indexDocument(
  index: string,
  id: string,
  document: any
): Promise<void> {
  try {
    const client = getElasticsearchClient();
    await client.index({
      index,
      id,
      body: document,
    });
  } catch (error) {
    console.error('Elasticsearch index error:', error);
    throw error;
  }
}

export async function deleteDocument(
  index: string,
  id: string
): Promise<void> {
  try {
    const client = getElasticsearchClient();
    await client.delete({
      index,
      id,
    });
  } catch (error) {
    console.error('Elasticsearch delete error:', error);
    throw error;
  }
}

// Health check utilities
export async function checkDatabaseHealth(): Promise<{
  mysql: boolean;
  redis: boolean;
  elasticsearch: boolean;
}> {
  const health = {
    mysql: false,
    redis: false,
    elasticsearch: false,
  };

  try {
    const connection = await getMySQLConnection();
    await connection.ping();
    health.mysql = true;
  } catch (error) {
    console.error('MySQL health check failed:', error);
  }

  try {
    const redis = await getRedisClient();
    await redis.ping();
    health.redis = true;
  } catch (error) {
    console.error('Redis health check failed:', error);
  }

  try {
    const client = getElasticsearchClient();
    await client.ping();
    health.elasticsearch = true;
  } catch (error) {
    console.error('Elasticsearch health check failed:', error);
  }

  return health;
}

// Cleanup connections
export async function closeConnections(): Promise<void> {
  try {
    if (mysqlConnection) {
      await mysqlConnection.end();
      mysqlConnection = null;
    }

    if (redisClient) {
      await redisClient.quit();
      redisClient = null;
    }

    if (elasticsearchClient) {
      await elasticsearchClient.close();
      elasticsearchClient = null;
    }
  } catch (error) {
    console.error('Error closing connections:', error);
  }
}
