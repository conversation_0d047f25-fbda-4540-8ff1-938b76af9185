'use client';

import React, { useState, useCallback } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Upload, 
  ArrowLeft,
  FileText,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  Download,
  Eye,
  Trash2,
  File,
  Code,
  Archive,
  Image
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function FileAnalyzer() {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const supportedTypes = [
    { ext: '.php', type: 'PHP Script', icon: <Code className="h-4 w-4" />, color: 'text-purple-500' },
    { ext: '.js', type: 'JavaScript', icon: <Code className="h-4 w-4" />, color: 'text-yellow-500' },
    { ext: '.py', type: 'Python Script', icon: <Code className="h-4 w-4" />, color: 'text-blue-500' },
    { ext: '.txt', type: 'Text File', icon: <FileText className="h-4 w-4" />, color: 'text-gray-500' },
    { ext: '.zip', type: 'ZIP Archive', icon: <Archive className="h-4 w-4" />, color: 'text-orange-500' },
    { ext: '.rar', type: 'RAR Archive', icon: <Archive className="h-4 w-4" />, color: 'text-red-500' },
    { ext: '.apk', type: 'Android APK', icon: <File className="h-4 w-4" />, color: 'text-green-500' },
    { ext: '.exe', type: 'Executable', icon: <File className="h-4 w-4" />, color: 'text-red-600' },
  ];

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (files: File[]) => {
    const newFiles = files.map(file => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending'
    }));
    
    setUploadedFiles(prev => [...prev, ...newFiles]);
  };

  const analyzeFile = async (fileData: any) => {
    setIsAnalyzing(true);
    
    // Update file status
    setUploadedFiles(prev => 
      prev.map(f => f.id === fileData.id ? { ...f, status: 'analyzing' } : f)
    );

    // Simulate analysis
    setTimeout(() => {
      const mockResults = generateMockAnalysis(fileData);
      setAnalysisResults(mockResults);
      
      setUploadedFiles(prev => 
        prev.map(f => f.id === fileData.id ? { 
          ...f, 
          status: 'completed',
          results: mockResults 
        } : f)
      );
      
      setIsAnalyzing(false);
    }, 3000);
  };

  const generateMockAnalysis = (fileData: any) => {
    const fileName = fileData.name.toLowerCase();
    
    if (fileName.endsWith('.php')) {
      return {
        threatLevel: 'malicious',
        isWebshell: true,
        malwareSignatures: ['PHP.Webshell.Generic', 'PHP.Backdoor.C99'],
        suspiciousFunctions: ['eval', 'exec', 'system', 'shell_exec', 'base64_decode'],
        secrets: [],
        fileInfo: {
          md5: 'a1b2c3d4e5f6789012345678901234567',
          sha256: 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
          entropy: 7.8,
          fileSize: fileData.size,
        },
        detectedThreats: [
          {
            type: 'Webshell',
            description: 'PHP webshell detected with command execution capabilities',
            severity: 'high',
            confidence: 0.95
          },
          {
            type: 'Backdoor',
            description: 'Backdoor functionality for remote access',
            severity: 'critical',
            confidence: 0.88
          }
        ]
      };
    } else if (fileName.endsWith('.js')) {
      return {
        threatLevel: 'suspicious',
        isWebshell: false,
        malwareSignatures: ['JS.Obfuscated.Generic'],
        suspiciousFunctions: ['eval', 'document.write', 'unescape'],
        secrets: ['api_key_12345', 'secret_token_abcdef'],
        fileInfo: {
          md5: 'b2c3d4e5f6789012345678901234567a',
          sha256: 'bcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890a',
          entropy: 6.2,
          fileSize: fileData.size,
        },
        detectedThreats: [
          {
            type: 'Obfuscated Code',
            description: 'JavaScript code appears to be obfuscated',
            severity: 'medium',
            confidence: 0.75
          },
          {
            type: 'Secret Exposure',
            description: 'API keys and tokens found in the code',
            severity: 'medium',
            confidence: 0.92
          }
        ]
      };
    } else {
      return {
        threatLevel: 'safe',
        isWebshell: false,
        malwareSignatures: [],
        suspiciousFunctions: [],
        secrets: [],
        fileInfo: {
          md5: 'c3d4e5f6789012345678901234567ab2',
          sha256: 'cdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
          entropy: 4.5,
          fileSize: fileData.size,
        },
        detectedThreats: []
      };
    }
  };

  const removeFile = (fileId: number) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
    if (analysisResults && uploadedFiles.find(f => f.id === fileId)?.results === analysisResults) {
      setAnalysisResults(null);
    }
  };

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'safe': return 'text-green-600 bg-green-100 border-green-200';
      case 'suspicious': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'malicious': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getFileIcon = (fileName: string) => {
    const ext = '.' + fileName.split('.').pop()?.toLowerCase();
    const fileType = supportedTypes.find(t => t.ext === ext);
    return fileType ? fileType.icon : <File className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/40 bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/dashboard/panel-user">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <FileText className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">File Analyzer</h1>
                <p className="text-muted-foreground">Analisis file berbahaya dan deteksi malware</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/file-analyzer/history">Riwayat Analisis</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Upload Area */}
          <div className="bg-card rounded-lg border border-border/40 p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Upload File untuk Analisis</h2>
            
            {/* Drag & Drop Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-primary bg-primary/10' 
                  : 'border-border/40 hover:border-primary/40'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium mb-2">
                Drag & drop file di sini atau klik untuk upload
              </p>
              <p className="text-muted-foreground mb-4">
                Maksimal 100MB per file
              </p>
              <input
                type="file"
                multiple
                onChange={handleFileInput}
                className="hidden"
                id="file-upload"
                accept=".php,.js,.py,.txt,.zip,.rar,.apk,.exe"
              />
              <Button asChild variant="outline">
                <label htmlFor="file-upload" className="cursor-pointer">
                  Pilih File
                </label>
              </Button>
            </div>

            {/* Supported File Types */}
            <div className="mt-6">
              <p className="text-sm font-medium mb-3">Tipe File yang Didukung:</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {supportedTypes.map((type, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className={type.color}>{type.icon}</span>
                    <span>{type.ext}</span>
                    <span className="text-muted-foreground">({type.type})</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Uploaded Files */}
          {uploadedFiles.length > 0 && (
            <div className="bg-card rounded-lg border border-border/40 p-6 mb-8">
              <h3 className="text-lg font-semibold mb-4">File yang Diupload</h3>
              <div className="space-y-3">
                {uploadedFiles.map((file) => (
                  <div key={file.id} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getFileIcon(file.name)}
                      <div>
                        <p className="font-medium">{file.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {file.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => analyzeFile(file)}
                          disabled={isAnalyzing}
                        >
                          Analisis
                        </Button>
                      )}
                      {file.status === 'analyzing' && (
                        <div className="flex items-center space-x-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">Menganalisis...</span>
                        </div>
                      )}
                      {file.status === 'completed' && (
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setAnalysisResults(file.results)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Lihat
                          </Button>
                        </div>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeFile(file.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Analysis Results */}
          {analysisResults && (
            <div className="bg-card rounded-lg border border-border/40 p-6">
              <h3 className="text-lg font-semibold mb-4">Hasil Analisis</h3>
              
              {/* Threat Level */}
              <div className="mb-6">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">Tingkat Ancaman:</span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getThreatLevelColor(analysisResults.threatLevel)}`}>
                    {analysisResults.threatLevel.toUpperCase()}
                  </span>
                </div>
              </div>

              {/* File Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="font-semibold mb-3">Informasi File</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">MD5:</span>
                      <span className="font-mono">{analysisResults.fileInfo.md5}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">SHA256:</span>
                      <span className="font-mono text-xs">{analysisResults.fileInfo.sha256}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Entropy:</span>
                      <span>{analysisResults.fileInfo.entropy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Size:</span>
                      <span>{formatFileSize(analysisResults.fileInfo.fileSize)}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Deteksi</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Webshell:</span>
                      <span className={analysisResults.isWebshell ? 'text-red-500' : 'text-green-500'}>
                        {analysisResults.isWebshell ? 'Terdeteksi' : 'Tidak'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Malware Signatures:</span>
                      <span>{analysisResults.malwareSignatures.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Suspicious Functions:</span>
                      <span>{analysisResults.suspiciousFunctions.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Secrets Found:</span>
                      <span>{analysisResults.secrets.length}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detected Threats */}
              {analysisResults.detectedThreats.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold mb-3">Ancaman Terdeteksi</h4>
                  <div className="space-y-3">
                    {analysisResults.detectedThreats.map((threat: any, index: number) => (
                      <div key={index} className="p-3 border border-border/40 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{threat.type}</span>
                          <span className={`px-2 py-1 rounded text-xs font-medium border ${
                            threat.severity === 'critical' ? 'text-red-600 bg-red-100 border-red-200' :
                            threat.severity === 'high' ? 'text-orange-600 bg-orange-100 border-orange-200' :
                            'text-yellow-600 bg-yellow-100 border-yellow-200'
                          }`}>
                            {threat.severity.toUpperCase()}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mb-1">{threat.description}</p>
                        <p className="text-xs text-muted-foreground">
                          Confidence: {(threat.confidence * 100).toFixed(1)}%
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Suspicious Functions */}
              {analysisResults.suspiciousFunctions.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold mb-3">Fungsi Mencurigakan</h4>
                  <div className="flex flex-wrap gap-2">
                    {analysisResults.suspiciousFunctions.map((func: string, index: number) => (
                      <span key={index} className="px-2 py-1 bg-red-100 text-red-800 rounded text-sm font-mono">
                        {func}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Secrets */}
              {analysisResults.secrets.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-3">Secret/Token Ditemukan</h4>
                  <div className="space-y-2">
                    {analysisResults.secrets.map((secret: string, index: number) => (
                      <div key={index} className="p-2 bg-yellow-100 text-yellow-800 rounded text-sm font-mono">
                        {secret}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
