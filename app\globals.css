@import "tailwindcss";

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;

    /* Cyberpunk accent colors */
    --cyber-pink: 320 100% 50%;
    --cyber-blue: 200 100% 50%;
    --cyber-green: 120 100% 50%;
    --cyber-purple: 280 100% 50%;
    --cyber-orange: 30 100% 50%;
  }

  .dark {
    /* Dark theme colors - Cyberpunk style */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;

    /* Cyberpunk dark accent colors */
    --cyber-pink: 320 100% 70%;
    --cyber-blue: 200 100% 70%;
    --cyber-green: 120 100% 70%;
    --cyber-purple: 280 100% 70%;
    --cyber-orange: 30 100% 70%;
  }
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: var(--font-inter);
  --font-mono: var(--font-mono);
}

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/80;
  }
}

@layer components {
  /* Cyberpunk glow effects */
  .cyber-glow {
    box-shadow: 0 0 20px hsl(var(--primary));
  }

  .cyber-glow-pink {
    box-shadow: 0 0 20px hsl(var(--cyber-pink));
  }

  .cyber-glow-blue {
    box-shadow: 0 0 20px hsl(var(--cyber-blue));
  }

  .cyber-glow-green {
    box-shadow: 0 0 20px hsl(var(--cyber-green));
  }

  /* Animated gradient backgrounds */
  .cyber-gradient {
    background: linear-gradient(
      45deg,
      hsl(var(--primary)),
      hsl(var(--cyber-pink)),
      hsl(var(--cyber-blue))
    );
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Neon text effect */
  .neon-text {
    color: hsl(var(--primary));
    text-shadow:
      0 0 5px hsl(var(--primary)),
      0 0 10px hsl(var(--primary)),
      0 0 15px hsl(var(--primary)),
      0 0 20px hsl(var(--primary));
  }

  /* Loading spinner */
  .cyber-spinner {
    border: 3px solid hsl(var(--secondary));
    border-top: 3px solid hsl(var(--primary));
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Pulse animation */
  .cyber-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
}
