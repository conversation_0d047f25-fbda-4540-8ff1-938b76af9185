# KodeXGuard - Platform Cybersecurity & Bug Hunting

![KodeXGuard Logo](https://img.shields.io/badge/KodeXGuard-Cybersecurity%20Platform-purple?style=for-the-badge&logo=shield&logoColor=white)

Platform mandiri untuk investigasi OSINT, vulnerability scanning, file analysis, CVE intelligence, dan bug hunting dengan dukungan bot automation.

## 🚀 Fitur Utama

### 🔍 OSINT Investigation
- Pencarian NIK, NPWP, IMEI, Email, Domain
- Tracker lokasi nomor dan IMEI
- Database leak (Dukcapil, Kemkes, GitHub)
- Social media profiling
- Data breach detection

### 🛡️ Vulnerability Scanner
- Deteksi SQLi, XSS, LFI, RCE, Path Traversal, CSRF
- Mapping ke CVSS/CVE
- Skor risiko (low → critical)
- Auto versioning hasil scan
- Concurrent scanning

### 📂 File Analyzer
- Deteksi webshell, ransomware, DDoS script
- Secret/Token/API key detection
- Support multiple file types (.php, .js, .py, .txt, .zip, .apk)
- Static & dynamic analysis
- Sandbox environment

### 💣 CVE Intelligence
- Database CVE terbaru dan terupdate
- Pencarian berda<PERSON> keyword, tahun, kategori
- Daily CVE alerts
- CVSS scoring
- Exploit availability tracking

### 🔍 Google Dorking
- Dork preset harian
- Custom dork builder
- Save & share dorks
- Advanced search operators

### 🤖 Bot Automation
- WhatsApp integration (venom.js)
- Telegram Bot API
- Scan via chat commands
- Real-time notifications
- Multi-user support

### 🧪 Developer Playground
- Swagger auto-documentation
- API testing interface
- Request builder
- Endpoint favorites
- Code examples

### 📊 Leaderboard & Community
- Bug hunter scoring system
- National statistics
- Community groups (WA/Telegram)
- Achievement system

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: TailwindCSS (Cyberpunk Theme)
- **State Management**: React Query (TanStack)
- **UI Components**: Radix UI + Custom Components
- **Icons**: Lucide React
- **Animations**: Framer Motion

### Backend
- **Runtime**: Bun.js
- **API**: RESTful JSON API
- **Authentication**: JWT + API Key
- **Database**: MySQL 8.0+
- **Cache**: Redis
- **Search**: Elasticsearch
- **File Storage**: Local/Cloud Storage

### Bot Integration
- **WhatsApp**: venom.js
- **Telegram**: Telegram Bot API

## 📦 Installation

### Prerequisites
- Node.js 18+ atau Bun.js
- MySQL 8.0+
- Redis
- Elasticsearch (optional)

### Quick Start

1. **Clone Repository**
```bash
git clone https://github.com/kodexguard/kodexguard.git
cd kodexguard
```

2. **Install Dependencies**
```bash
# Using Bun (Recommended)
bun install

# Or using npm
npm install
```

3. **Environment Setup**
```bash
cp .env.local.example .env.local
```

Edit `.env.local` dengan konfigurasi Anda:
```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/kodexguard"
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="username"
DB_PASSWORD="password"
DB_NAME="kodexguard"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key"

# API
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
```

4. **Database Setup**
```bash
# Import database schema
mysql -u username -p kodexguard < docs/database-schema.sql
```

5. **Run Development Server**
```bash
# Using Bun
bun run dev

# Or using npm
npm run dev
```

6. **Open Browser**
```
http://localhost:3000
```

## 🎯 Usage

### User Registration & Authentication
1. Buka halaman utama
2. Klik "Dashboard" untuk registrasi/login
3. Pilih plan sesuai kebutuhan
4. Generate API key untuk akses programmatic

### OSINT Investigation
1. Masuk ke menu "OSINT"
2. Pilih jenis pencarian (NIK, NPWP, Email, dll)
3. Masukkan query
4. Lihat hasil investigasi real-time

### Vulnerability Scanning
1. Akses "Scanner" dari menu
2. Input target URL/domain
3. Pilih jenis scan
4. Monitor progress dan hasil

### File Analysis
1. Buka "File Analyzer"
2. Upload file yang ingin dianalisis
3. Tunggu proses analisis
4. Review laporan keamanan

## 🔧 Configuration

### Plan & Pricing
Platform mendukung 5 tier subscription:

- **Free**: Basic features, limited quota
- **Student**: Enhanced features untuk pelajar
- **Hobby**: Professional tools untuk hobbyist
- **Bug Hunter**: Advanced features untuk researcher
- **Cybersecurity**: Enterprise-grade unlimited access

### Bot Setup
1. **WhatsApp Bot**:
   - Scan QR code dari admin panel
   - Bot akan terhubung otomatis
   - Users dapat menggunakan commands via chat

2. **Telegram Bot**:
   - Create bot via @BotFather
   - Add bot token ke environment
   - Configure webhook URL

## 🚀 Deployment

### Production Build
```bash
bun run build
bun start
```

### Docker Deployment
```bash
docker-compose up -d
```

### Environment Variables
Pastikan semua environment variables sudah dikonfigurasi untuk production:
- Database credentials
- Redis connection
- JWT secrets
- API keys
- Bot tokens

## 📚 API Documentation

API documentation tersedia di `/api/docs` setelah aplikasi berjalan.

### Authentication
```bash
# Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# API Key Usage
GET /api/osint/search
Headers: {
  "X-API-Key": "your-api-key"
}
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🛡️ Security

Untuk melaporkan vulnerability, silakan email ke: <EMAIL>

## 📞 Support

- **Documentation**: [docs.kodexguard.com](https://docs.kodexguard.com)
- **Community**: [Telegram Group](https://t.me/kodexguard)
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/kodexguard/kodexguard/issues)

## 🎉 Acknowledgments

- Tim pengembang KodeXGuard
- Community contributors
- Open source libraries yang digunakan

---

**KodeXGuard** - Empowering Cybersecurity Professionals 🛡️
