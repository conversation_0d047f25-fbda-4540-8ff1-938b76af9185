{"name": "kodexguard", "version": "1.0.0", "private": true, "description": "KodeXGuard - Platform Cybersecurity & Bug Hunting Mandiri", "keywords": ["cybersecurity", "osint", "vulnerability-scanner", "bug-hunting", "penetration-testing"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@elastic/elasticsearch": "^8.11.0", "@hookform/resolvers": "^3.3.0", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.294.0", "multer": "^1.4.4", "mysql2": "^3.6.0", "next": "15.4.1", "next-themes": "^0.4.6", "node-telegram-bot-api": "^0.64.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "recharts": "^2.8.0", "redis": "^4.6.0", "sharp": "^0.33.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "venom-bot": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/multer": "^1.4.0", "@types/node-telegram-bot-api": "^0.64.0", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "@eslint/eslintrc": "^3"}}