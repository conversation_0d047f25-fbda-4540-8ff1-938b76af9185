'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  Shield, 
  Check, 
  Star, 
  ArrowLeft,
  Zap,
  Users,
  Crown,
  Rocket,
  Gift
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  PLANS,
  PLAN_PRICES,
  PLAN_FEATURES,
  PLAN_LABELS,
  PLAN_DESCRIPTIONS,
  POPULAR_PLANS,
  formatPrice,
  isPopularPlan
} from '@/constants/plans';
import { PlanDuration, PlanType } from '@/types/plan';

export default function PricingPage() {
  const [selectedDuration, setSelectedDuration] = useState<PlanDuration>(PlanDuration.MONTHLY);

  const durations = [
    { value: PlanDuration.DAILY, label: 'Harian', discount: 0 },
    { value: PlanDuration.WEEKLY, label: 'Mingguan', discount: 10 },
    { value: PlanDuration.MONTHLY, label: '<PERSON>ulanan', discount: 20 },
    { value: PlanDuration.YEARLY, label: '<PERSON><PERSON><PERSON>', discount: 30 },
  ];

  const planIcons = {
    [PLANS.FREE]: <Gift className="h-6 w-6" />,
    [PLANS.STUDENT]: <Users className="h-6 w-6" />,
    [PLANS.HOBBY]: <Zap className="h-6 w-6" />,
    [PLANS.BUGHUNTER]: <Shield className="h-6 w-6" />,
    [PLANS.CYBERSECURITY]: <Crown className="h-6 w-6" />,
  };

  const planColors = {
    [PLANS.FREE]: 'border-gray-200 hover:border-gray-300',
    [PLANS.STUDENT]: 'border-blue-200 hover:border-blue-300',
    [PLANS.HOBBY]: 'border-green-200 hover:border-green-300 ring-2 ring-green-500/20',
    [PLANS.BUGHUNTER]: 'border-purple-200 hover:border-purple-300 ring-2 ring-purple-500/20',
    [PLANS.CYBERSECURITY]: 'border-red-200 hover:border-red-300',
  };

  const handleSubscribe = (planType: string) => {
    // In real app, this would redirect to payment or show payment modal
    console.log(`Subscribe to ${planType} for ${selectedDuration}`);
    alert(`Redirecting to payment for ${PLAN_LABELS[planType as keyof typeof PLAN_LABELS]} plan...`);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/40 bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <Rocket className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">Pricing Plans</h1>
                <p className="text-muted-foreground">Pilih plan yang sesuai dengan kebutuhan Anda</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/dashboard/panel-user">Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Duration Selector */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Pilih Durasi Langganan</h2>
          <p className="text-muted-foreground mb-8">Hemat lebih banyak dengan langganan jangka panjang</p>
          
          <div className="inline-flex bg-secondary/20 rounded-lg p-1">
            {durations.map((duration) => (
              <button
                key={duration.value}
                onClick={() => setSelectedDuration(duration.value)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
                  selectedDuration === duration.value
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {duration.label}
                {duration.discount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1 rounded-full">
                    -{duration.discount}%
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 max-w-7xl mx-auto">
          {Object.values(PLANS).map((planType) => {
            const isPopular = isPopularPlan(planType as PlanType);
            const price = PLAN_PRICES[planType][selectedDuration];
            const features = PLAN_FEATURES[planType];
            
            return (
              <div
                key={planType}
                className={`relative bg-card rounded-lg border p-6 transition-all hover:shadow-lg ${
                  planColors[planType]
                } ${isPopular ? 'scale-105' : ''}`}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      Popular
                    </span>
                  </div>
                )}

                {/* Plan Header */}
                <div className="text-center mb-6">
                  <div className="flex justify-center mb-3 text-primary">
                    {planIcons[planType]}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{PLAN_LABELS[planType]}</h3>
                  <p className="text-muted-foreground text-sm mb-4">
                    {PLAN_DESCRIPTIONS[planType]}
                  </p>
                  
                  {/* Price */}
                  <div className="mb-4">
                    {price === 0 ? (
                      <div className="text-3xl font-bold">Gratis</div>
                    ) : (
                      <>
                        <div className="text-3xl font-bold">{formatPrice(price)}</div>
                        <div className="text-muted-foreground text-sm">
                          per {selectedDuration === PlanDuration.DAILY ? 'hari' : 
                               selectedDuration === PlanDuration.WEEKLY ? 'minggu' :
                               selectedDuration === PlanDuration.MONTHLY ? 'bulan' : 'tahun'}
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <Button
                  onClick={() => handleSubscribe(planType)}
                  className={`w-full ${
                    isPopular 
                      ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                      : ''
                  }`}
                  variant={isPopular ? 'default' : 'outline'}
                >
                  {planType === PLANS.FREE ? 'Mulai Gratis' : 'Pilih Plan'}
                </Button>
              </div>
            );
          })}
        </div>

        {/* Features Comparison */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-center mb-8">Perbandingan Fitur Lengkap</h3>
          
          <div className="bg-card rounded-lg border border-border/40 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-secondary/20">
                  <tr>
                    <th className="text-left p-4 font-semibold">Fitur</th>
                    {Object.values(PLANS).map((planType) => (
                      <th key={planType} className="text-center p-4 font-semibold">
                        {PLAN_LABELS[planType]}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <FeatureRow 
                    feature="API Calls per Day" 
                    values={{
                      [PLANS.FREE]: '50',
                      [PLANS.STUDENT]: '200',
                      [PLANS.HOBBY]: '500',
                      [PLANS.BUGHUNTER]: '2,000',
                      [PLANS.CYBERSECURITY]: 'Unlimited',
                    }}
                  />
                  <FeatureRow 
                    feature="Vulnerability Scans per Day" 
                    values={{
                      [PLANS.FREE]: '5',
                      [PLANS.STUDENT]: '20',
                      [PLANS.HOBBY]: '50',
                      [PLANS.BUGHUNTER]: '200',
                      [PLANS.CYBERSECURITY]: 'Unlimited',
                    }}
                  />
                  <FeatureRow 
                    feature="File Upload Size" 
                    values={{
                      [PLANS.FREE]: '5 MB',
                      [PLANS.STUDENT]: '10 MB',
                      [PLANS.HOBBY]: '25 MB',
                      [PLANS.BUGHUNTER]: '100 MB',
                      [PLANS.CYBERSECURITY]: '500 MB',
                    }}
                  />
                  <FeatureRow 
                    feature="Data Retention" 
                    values={{
                      [PLANS.FREE]: '7 days',
                      [PLANS.STUDENT]: '30 days',
                      [PLANS.HOBBY]: '90 days',
                      [PLANS.BUGHUNTER]: '180 days',
                      [PLANS.CYBERSECURITY]: '365 days',
                    }}
                  />
                  <FeatureRow 
                    feature="Support Level" 
                    values={{
                      [PLANS.FREE]: 'Community',
                      [PLANS.STUDENT]: 'Email',
                      [PLANS.HOBBY]: 'Email',
                      [PLANS.BUGHUNTER]: 'Priority',
                      [PLANS.CYBERSECURITY]: 'Dedicated',
                    }}
                  />
                  <FeatureRow 
                    feature="Custom Dorks" 
                    values={{
                      [PLANS.FREE]: '❌',
                      [PLANS.STUDENT]: '✅',
                      [PLANS.HOBBY]: '✅',
                      [PLANS.BUGHUNTER]: '✅',
                      [PLANS.CYBERSECURITY]: '✅',
                    }}
                  />
                  <FeatureRow 
                    feature="Advanced Features" 
                    values={{
                      [PLANS.FREE]: '❌',
                      [PLANS.STUDENT]: '❌',
                      [PLANS.HOBBY]: '✅',
                      [PLANS.BUGHUNTER]: '✅',
                      [PLANS.CYBERSECURITY]: '✅',
                    }}
                  />
                  <FeatureRow 
                    feature="Priority Queue" 
                    values={{
                      [PLANS.FREE]: '❌',
                      [PLANS.STUDENT]: '❌',
                      [PLANS.HOBBY]: '❌',
                      [PLANS.BUGHUNTER]: '✅',
                      [PLANS.CYBERSECURITY]: '✅',
                    }}
                  />
                  <FeatureRow 
                    feature="White Label" 
                    values={{
                      [PLANS.FREE]: '❌',
                      [PLANS.STUDENT]: '❌',
                      [PLANS.HOBBY]: '❌',
                      [PLANS.BUGHUNTER]: '❌',
                      [PLANS.CYBERSECURITY]: '✅',
                    }}
                  />
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-4">Pertanyaan Umum</h3>
          <p className="text-muted-foreground mb-8">
            Punya pertanyaan? Hubungi tim support kami di{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <div className="text-left">
              <h4 className="font-semibold mb-2">Apakah ada trial gratis?</h4>
              <p className="text-muted-foreground text-sm">
                Ya, plan Free tersedia selamanya dengan fitur terbatas. Anda juga bisa upgrade kapan saja.
              </p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold mb-2">Bagaimana cara pembayaran?</h4>
              <p className="text-muted-foreground text-sm">
                Kami menerima transfer bank, e-wallet, dan berbagai metode pembayaran digital lainnya.
              </p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold mb-2">Bisakah downgrade plan?</h4>
              <p className="text-muted-foreground text-sm">
                Ya, Anda bisa downgrade plan kapan saja. Perubahan akan berlaku di periode billing berikutnya.
              </p>
            </div>
            <div className="text-left">
              <h4 className="font-semibold mb-2">Apakah data aman?</h4>
              <p className="text-muted-foreground text-sm">
                Semua data dienkripsi dan disimpan dengan standar keamanan tinggi. Kami tidak membagikan data Anda.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function FeatureRow({ feature, values }: { 
  feature: string; 
  values: Record<string, string> 
}) {
  return (
    <tr className="border-t border-border/40">
      <td className="p-4 font-medium">{feature}</td>
      {Object.values(PLANS).map((planType) => (
        <td key={planType} className="p-4 text-center">
          {values[planType]}
        </td>
      ))}
    </tr>
  );
}
