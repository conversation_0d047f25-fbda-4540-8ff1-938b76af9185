-- KodeXGuard Database Schema
-- MySQL 8.0+ Compatible

-- Create database
CREATE DATABASE IF NOT EXISTS kodexguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kodexguard;

-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    avatar VARCHAR(500),
    bio TEXT,
    role ENUM('super_admin', 'admin', 'user') DEFAULT 'user',
    plan ENUM('free', 'student', 'hobby', 'bughunter', 'cybersecurity') DEFAULT 'free',
    plan_expiry DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    api_key VARCHAR(64) UNIQUE,
    api_usage INT DEFAULT 0,
    api_limit INT DEFAULT 1000,
    score INT DEFAULT 0,
    total_scans INT DEFAULT 0,
    total_exploits INT DEFAULT 0,
    total_files INT DEFAULT 0,
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_api_key (api_key),
    INDEX idx_role (role),
    INDEX idx_plan (plan),
    INDEX idx_is_active (is_active)
);

-- User sessions table
CREATE TABLE user_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- User activities table
CREATE TABLE user_activities (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Plans table
CREATE TABLE plans (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    type ENUM('free', 'student', 'hobby', 'bughunter', 'cybersecurity') UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'IDR',
    duration ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL,
    features JSON,
    limits JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    plan_id VARCHAR(36) NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'pending', 'suspended') DEFAULT 'pending',
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    auto_renew BOOLEAN DEFAULT FALSE,
    payment_method ENUM('manual_transfer', 'midtrans', 'tripay', 'xendit', 'paypal', 'stripe') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- Payments table
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    subscription_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    method ENUM('manual_transfer', 'midtrans', 'tripay', 'xendit', 'paypal', 'stripe') NOT NULL,
    status ENUM('pending', 'paid', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    gateway_response JSON,
    paid_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_transaction_id (transaction_id)
);

-- OSINT searches table
CREATE TABLE osint_searches (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    type ENUM('nik', 'npwp', 'phone', 'imei', 'email', 'domain', 'ip', 'name', 'address', 'social_media') NOT NULL,
    query VARCHAR(500) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    results JSON,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Vulnerability scans table
CREATE TABLE vulnerability_scans (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    target_url VARCHAR(1000) NOT NULL,
    scan_type VARCHAR(100) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    vulnerabilities JSON,
    risk_score INT DEFAULT 0,
    cvss_score DECIMAL(3,1),
    cve_mappings JSON,
    scan_duration INT, -- in seconds
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_risk_score (risk_score),
    INDEX idx_created_at (created_at)
);

-- File analyses table
CREATE TABLE file_analyses (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100),
    file_hash VARCHAR(64),
    status ENUM('pending', 'analyzing', 'completed', 'failed') DEFAULT 'pending',
    analysis_results JSON,
    threat_level ENUM('safe', 'suspicious', 'malicious', 'unknown') DEFAULT 'unknown',
    detected_threats JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_threat_level (threat_level),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- CVE database table
CREATE TABLE cve_database (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    cve_id VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    cvss_score DECIMAL(3,1),
    cvss_vector VARCHAR(100),
    published_date DATE,
    modified_date DATE,
    affected_products JSON,
    references JSON,
    exploit_available BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_cve_id (cve_id),
    INDEX idx_severity (severity),
    INDEX idx_cvss_score (cvss_score),
    INDEX idx_published_date (published_date)
);

-- Bot configurations table
CREATE TABLE bot_configurations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    type ENUM('whatsapp', 'telegram') NOT NULL,
    name VARCHAR(100) NOT NULL,
    config JSON NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    is_connected BOOLEAN DEFAULT FALSE,
    last_activity DATETIME,
    created_by VARCHAR(36) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_type (type),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
);

-- System settings table
CREATE TABLE system_settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    key_name VARCHAR(100) UNIQUE NOT NULL,
    value JSON,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key_name (key_name),
    INDEX idx_is_public (is_public)
);

-- Insert default plans
INSERT INTO plans (name, type, price, duration, features, limits, is_popular, description) VALUES
('Free Plan', 'free', 0, 'monthly', 
 '["Basic OSINT searches", "Limited vulnerability scanning", "Basic file analysis", "Community support"]',
 '{"apiCallsPerDay": 50, "scanPerDay": 5, "fileUploadSize": 5, "supportLevel": "community"}',
 FALSE, 'Perfect for getting started with basic cybersecurity tools'),

('Student Plan', 'student', 75000, 'monthly',
 '["Enhanced OSINT searches", "Advanced vulnerability scanning", "Email support", "Educational resources"]',
 '{"apiCallsPerDay": 200, "scanPerDay": 20, "fileUploadSize": 10, "supportLevel": "email"}',
 FALSE, 'Ideal for students learning cybersecurity and ethical hacking'),

('Hobby Plan', 'hobby', 250000, 'monthly',
 '["Professional OSINT tools", "Advanced scanning engines", "Custom dorks", "Advanced reporting"]',
 '{"apiCallsPerDay": 500, "scanPerDay": 50, "fileUploadSize": 25, "supportLevel": "email"}',
 TRUE, 'Great for hobbyists and part-time security researchers'),

('Bug Hunter Plan', 'bughunter', 850000, 'monthly',
 '["Enterprise OSINT capabilities", "High-performance scanning", "Priority support", "Advanced analytics"]',
 '{"apiCallsPerDay": 2000, "scanPerDay": 200, "fileUploadSize": 100, "supportLevel": "priority"}',
 TRUE, 'Professional tools for serious bug hunters and researchers'),

('Cybersecurity Pro', 'cybersecurity', 2500000, 'monthly',
 '["Unlimited everything", "Enterprise-grade security", "Dedicated support", "Custom integrations"]',
 '{"apiCallsPerDay": -1, "scanPerDay": -1, "fileUploadSize": 500, "supportLevel": "dedicated"}',
 FALSE, 'Enterprise-grade platform for cybersecurity professionals');

-- Insert default system settings
INSERT INTO system_settings (key_name, value, description, is_public) VALUES
('app_name', '"KodeXGuard"', 'Application name', TRUE),
('app_version', '"1.0.0"', 'Application version', TRUE),
('maintenance_mode', 'false', 'Maintenance mode toggle', FALSE),
('registration_enabled', 'true', 'User registration enabled', TRUE),
('max_file_upload_size', '104857600', 'Maximum file upload size in bytes (100MB)', FALSE),
('default_api_rate_limit', '1000', 'Default API rate limit per day', FALSE);
