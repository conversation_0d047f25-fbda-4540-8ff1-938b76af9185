import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { randomBytes } from 'crypto';
import { executeQuery, cacheSet, cacheGet, cacheDelete } from './database';
import { User, UserRole, UserPlan, LoginResponse } from '@/types/user';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

export class AuthError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'AuthError';
  }
}

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, BCRYPT_ROUNDS);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// JWT utilities
export function generateToken(payload: any): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    throw new AuthError('Invalid token');
  }
}

// API Key utilities
export function generateApiKey(): string {
  return randomBytes(32).toString('hex');
}

// User authentication
export async function authenticateUser(email: string, password: string): Promise<LoginResponse> {
  const users = await executeQuery<User>(
    'SELECT * FROM users WHERE email = ? AND is_active = TRUE',
    [email]
  );

  if (users.length === 0) {
    throw new AuthError('Invalid credentials');
  }

  const user = users[0];
  const isValidPassword = await verifyPassword(password, user.password_hash);

  if (!isValidPassword) {
    throw new AuthError('Invalid credentials');
  }

  // Update last login
  await executeQuery(
    'UPDATE users SET last_login_at = NOW() WHERE id = ?',
    [user.id]
  );

  // Generate tokens
  const token = generateToken({ userId: user.id, email: user.email, role: user.role });
  const refreshToken = randomBytes(32).toString('hex');

  // Store session
  const sessionId = randomBytes(16).toString('hex');
  await executeQuery(
    `INSERT INTO user_sessions (id, user_id, token, refresh_token, expires_at, ip_address, user_agent)
     VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 7 DAY), ?, ?)`,
    [sessionId, user.id, token, refreshToken, '', ''] // IP and user agent should be passed from request
  );

  // Cache user data
  await cacheSet(`user:${user.id}`, user, 3600);

  // Remove password hash from response
  const { password_hash, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword as User,
    token,
    refreshToken,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  };
}

// Get user by token
export async function getUserByToken(token: string): Promise<User | null> {
  try {
    const decoded = verifyToken(token);
    const userId = decoded.userId;

    // Try cache first
    let user = await cacheGet<User>(`user:${userId}`);
    
    if (!user) {
      const users = await executeQuery<User>(
        'SELECT * FROM users WHERE id = ? AND is_active = TRUE',
        [userId]
      );

      if (users.length === 0) {
        return null;
      }

      user = users[0];
      await cacheSet(`user:${userId}`, user, 3600);
    }

    // Remove password hash
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    return null;
  }
}

// Create user
export async function createUser(userData: {
  username: string;
  email: string;
  password: string;
  fullName: string;
  role?: UserRole;
  plan?: UserPlan;
}): Promise<User> {
  const { username, email, password, fullName, role = UserRole.USER, plan = UserPlan.FREE } = userData;

  // Check if user already exists
  const existingUsers = await executeQuery(
    'SELECT id FROM users WHERE email = ? OR username = ?',
    [email, username]
  );

  if (existingUsers.length > 0) {
    throw new AuthError('User already exists');
  }

  // Hash password
  const passwordHash = await hashPassword(password);

  // Generate API key
  const apiKey = generateApiKey();

  // Insert user
  const userId = randomBytes(16).toString('hex');
  await executeQuery(
    `INSERT INTO users (id, username, email, password_hash, full_name, role, plan, api_key)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
    [userId, username, email, passwordHash, fullName, role, plan, apiKey]
  );

  // Get created user
  const users = await executeQuery<User>(
    'SELECT * FROM users WHERE id = ?',
    [userId]
  );

  const user = users[0];
  const { password_hash, ...userWithoutPassword } = user;
  
  return userWithoutPassword as User;
}

// Update user
export async function updateUser(userId: string, updates: Partial<User>): Promise<User> {
  const allowedFields = ['full_name', 'bio', 'avatar', 'role', 'plan', 'is_active', 'is_verified'];
  const updateFields: string[] = [];
  const updateValues: any[] = [];

  Object.entries(updates).forEach(([key, value]) => {
    if (allowedFields.includes(key) && value !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(value);
    }
  });

  if (updateFields.length === 0) {
    throw new AuthError('No valid fields to update');
  }

  updateValues.push(userId);

  await executeQuery(
    `UPDATE users SET ${updateFields.join(', ')}, updated_at = NOW() WHERE id = ?`,
    updateValues
  );

  // Clear cache
  await cacheDelete(`user:${userId}`);

  // Get updated user
  const users = await executeQuery<User>(
    'SELECT * FROM users WHERE id = ?',
    [userId]
  );

  const user = users[0];
  const { password_hash, ...userWithoutPassword } = user;
  
  return userWithoutPassword as User;
}

// Change password
export async function changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
  const users = await executeQuery<{ password_hash: string }>(
    'SELECT password_hash FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new AuthError('User not found');
  }

  const isValidPassword = await verifyPassword(currentPassword, users[0].password_hash);
  if (!isValidPassword) {
    throw new AuthError('Current password is incorrect');
  }

  const newPasswordHash = await hashPassword(newPassword);
  await executeQuery(
    'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
    [newPasswordHash, userId]
  );

  // Invalidate all user sessions
  await executeQuery(
    'UPDATE user_sessions SET is_active = FALSE WHERE user_id = ?',
    [userId]
  );

  // Clear cache
  await cacheDelete(`user:${userId}`);
}

// Generate new API key
export async function regenerateApiKey(userId: string): Promise<string> {
  const newApiKey = generateApiKey();
  
  await executeQuery(
    'UPDATE users SET api_key = ?, updated_at = NOW() WHERE id = ?',
    [newApiKey, userId]
  );

  // Clear cache
  await cacheDelete(`user:${userId}`);

  return newApiKey;
}

// Validate API key
export async function validateApiKey(apiKey: string): Promise<User | null> {
  try {
    // Try cache first
    let user = await cacheGet<User>(`apikey:${apiKey}`);
    
    if (!user) {
      const users = await executeQuery<User>(
        'SELECT * FROM users WHERE api_key = ? AND is_active = TRUE',
        [apiKey]
      );

      if (users.length === 0) {
        return null;
      }

      user = users[0];
      await cacheSet(`apikey:${apiKey}`, user, 1800); // 30 minutes cache
    }

    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    return null;
  }
}

// Log user activity
export async function logUserActivity(
  userId: string,
  action: string,
  description: string,
  metadata?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await executeQuery(
      `INSERT INTO user_activities (user_id, action, description, metadata, ip_address, user_agent)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [userId, action, description, metadata ? JSON.stringify(metadata) : null, ipAddress, userAgent]
    );
  } catch (error) {
    console.error('Failed to log user activity:', error);
  }
}

// Logout user
export async function logoutUser(token: string): Promise<void> {
  try {
    await executeQuery(
      'UPDATE user_sessions SET is_active = FALSE WHERE token = ?',
      [token]
    );
  } catch (error) {
    console.error('Failed to logout user:', error);
  }
}

// Check user permissions
export function hasPermission(user: User, permission: string): boolean {
  // Import role permissions from constants
  const { ROLE_PERMISSIONS } = require('@/constants/roles');
  const permissions = ROLE_PERMISSIONS[user.role] || [];
  return permissions.includes(permission);
}

export function hasAnyPermission(user: User, permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(user, permission));
}

export function hasAllPermissions(user: User, permissions: string[]): boolean {
  return permissions.every(permission => hasPermission(user, permission));
}
