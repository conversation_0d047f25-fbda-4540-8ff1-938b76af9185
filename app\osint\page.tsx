'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Search, 
  User, 
  Phone, 
  Mail, 
  Globe, 
  MapPin, 
  CreditCard,
  Smartphone,
  ArrowLeft,
  Loader2,
  AlertCircle,
  CheckCircle,
  Copy
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function OSINTPage() {
  const [searchType, setSearchType] = useState('nik');
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<any>(null);

  const searchTypes = [
    { id: 'nik', label: 'NIK', icon: <User className="h-4 w-4" />, placeholder: 'Masukkan NIK (16 digit)' },
    { id: 'npwp', label: 'NPWP', icon: <CreditCard className="h-4 w-4" />, placeholder: 'Masukkan NPWP' },
    { id: 'phone', label: 'Nomor HP', icon: <Phone className="h-4 w-4" />, placeholder: 'Masukkan nomor HP' },
    { id: 'imei', label: 'IMEI', icon: <Smartphone className="h-4 w-4" />, placeholder: 'Masukkan IMEI' },
    { id: 'email', label: 'Email', icon: <Mail className="h-4 w-4" />, placeholder: 'Masukkan alamat email' },
    { id: 'domain', label: 'Domain', icon: <Globe className="h-4 w-4" />, placeholder: 'Masukkan domain' },
    { id: 'name', label: 'Nama', icon: <User className="h-4 w-4" />, placeholder: 'Masukkan nama lengkap' },
    { id: 'address', label: 'Alamat', icon: <MapPin className="h-4 w-4" />, placeholder: 'Masukkan alamat' },
  ];

  const handleSearch = async () => {
    if (!query.trim()) return;

    setIsSearching(true);
    
    // Simulate API call
    setTimeout(() => {
      // Mock results based on search type
      const mockResults = generateMockResults(searchType, query);
      setResults(mockResults);
      setIsSearching(false);
    }, 2000);
  };

  const generateMockResults = (type: string, query: string) => {
    switch (type) {
      case 'nik':
        return {
          type: 'NIK',
          query,
          found: true,
          data: {
            nik: query,
            name: 'John Doe',
            birthPlace: 'Jakarta',
            birthDate: '1990-01-15',
            gender: 'Laki-laki',
            address: 'Jl. Sudirman No. 123, Jakarta Pusat',
            province: 'DKI Jakarta',
            city: 'Jakarta Pusat',
            district: 'Tanah Abang',
            village: 'Bendungan Hilir',
            postalCode: '10210',
            isValid: true,
            sources: ['Dukcapil Database', 'KTP Online']
          }
        };
      case 'email':
        return {
          type: 'Email',
          query,
          found: true,
          data: {
            email: query,
            isValid: true,
            domain: query.split('@')[1],
            socialMedia: [
              { platform: 'Facebook', username: 'john.doe', verified: false },
              { platform: 'LinkedIn', username: 'johndoe', verified: true },
            ],
            dataBreaches: [
              { name: 'LinkedIn Breach 2021', date: '2021-06-01', severity: 'High' },
              { name: 'Facebook Leak 2019', date: '2019-04-01', severity: 'Medium' },
            ],
            sources: ['HIBP Database', 'Social Media Scan']
          }
        };
      case 'phone':
        return {
          type: 'Phone',
          query,
          found: true,
          data: {
            phone: query,
            carrier: 'Telkomsel',
            type: 'Mobile',
            location: 'Jakarta, Indonesia',
            isActive: true,
            socialMedia: [
              { platform: 'WhatsApp', verified: true },
              { platform: 'Telegram', username: '@johndoe', verified: false },
            ],
            sources: ['Carrier Database', 'Social Media Scan']
          }
        };
      default:
        return {
          type: searchType.toUpperCase(),
          query,
          found: false,
          message: 'Data tidak ditemukan atau tidak tersedia'
        };
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/dashboard/panel-user">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <Search className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">OSINT Investigation</h1>
                <p className="text-muted-foreground">Investigasi mendalam data personal dan digital</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/osint/history">Riwayat</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Search Form */}
          <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <h2 className="text-xl font-semibold mb-6">Pilih Jenis Pencarian</h2>
            
            {/* Search Type Selector */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
              {searchTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSearchType(type.id)}
                  className={`p-3 rounded-lg border transition-all ${
                    searchType === type.id
                      ? 'border-primary bg-primary/10 text-primary'
                      : 'border-gray-200 dark:border-gray-700 hover:border-primary/40'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    {type.icon}
                    <span className="text-sm font-medium">{type.label}</span>
                  </div>
                </button>
              ))}
            </div>

            {/* Search Input */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={searchTypes.find(t => t.id === searchType)?.placeholder}
                  className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-background focus:border-primary focus:outline-none"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button 
                onClick={handleSearch}
                disabled={!query.trim() || isSearching}
                className="px-8"
                variant="cyber"
              >
                {isSearching ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Mencari...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Cari
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Results */}
          {results && (
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Hasil Pencarian</h2>
                <div className="flex items-center space-x-2">
                  {results.found ? (
                    <div className="flex items-center text-green-500">
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Data Ditemukan
                    </div>
                  ) : (
                    <div className="flex items-center text-yellow-500">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      Data Tidak Ditemukan
                    </div>
                  )}
                </div>
              </div>

              {results.found ? (
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(results.data).map(([key, value]) => {
                      if (typeof value === 'object' || key === 'sources') return null;
                      
                      return (
                        <div key={key} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                          <span className="text-muted-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}:
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{String(value)}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => copyToClipboard(String(value))}
                              className="h-6 w-6"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Social Media */}
                  {results.data.socialMedia && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Media Sosial</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {results.data.socialMedia.map((social: any, index: number) => (
                          <div key={index} className="p-3 bg-secondary/20 rounded-lg">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{social.platform}</span>
                              {social.verified && (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              )}
                            </div>
                            {social.username && (
                              <p className="text-muted-foreground text-sm">@{social.username}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Data Breaches */}
                  {results.data.dataBreaches && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Data Breach</h3>
                      <div className="space-y-3">
                        {results.data.dataBreaches.map((breach: any, index: number) => (
                          <div key={index} className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{breach.name}</span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                breach.severity === 'High' ? 'bg-red-500/20 text-red-500' :
                                breach.severity === 'Medium' ? 'bg-yellow-500/20 text-yellow-500' :
                                'bg-green-500/20 text-green-500'
                              }`}>
                                {breach.severity}
                              </span>
                            </div>
                            <p className="text-muted-foreground text-sm">{breach.date}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Sources */}
                  {results.data.sources && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Sumber Data</h3>
                      <div className="flex flex-wrap gap-2">
                        {results.data.sources.map((source: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                          >
                            {source}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">{results.message}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
