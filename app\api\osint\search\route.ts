import { NextRequest, NextResponse } from 'next/server';
import { getUserByToken, validateApi<PERSON><PERSON> } from '@/lib/auth';
import { executeQuery, indexDocument } from '@/lib/database';
import { z } from 'zod';
import { randomBytes } from 'crypto';

// Validation schema
const osintSearchSchema = z.object({
  type: z.enum(['nik', 'npwp', 'phone', 'imei', 'email', 'domain', 'ip', 'name', 'address', 'social_media']),
  query: z.string().min(1, 'Query tidak boleh kosong').max(500, 'Query terlalu panjang'),
  options: z.object({
    deepSearch: z.boolean().optional().default(false),
    includeSocialMedia: z.boolean().optional().default(true),
    includeDataBreaches: z.boolean().optional().default(true),
    maxResults: z.number().min(1).max(1000).optional().default(100),
  }).optional().default({}),
});

// Get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  // Try JWT token first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const user = await getUserByToken(token);
    if (user) return user;
  }

  // Try API key
  const apiKey = request.headers.get('x-api-key');
  if (apiKey) {
    const user = await validateApiKey(apiKey);
    if (user) return user;
  }

  throw new Error('Authentication required');
}

// Mock OSINT search function (replace with real implementation)
async function performOSINTSearch(type: string, query: string, options: any) {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock results based on search type
  const mockResults = {
    nik: {
      found: true,
      data: {
        nik: query,
        name: 'John Doe',
        birthPlace: 'Jakarta',
        birthDate: '1990-01-15',
        gender: 'Laki-laki',
        address: 'Jl. Sudirman No. 123, Jakarta Pusat',
        province: 'DKI Jakarta',
        city: 'Jakarta Pusat',
        district: 'Tanah Abang',
        village: 'Bendungan Hilir',
        postalCode: '10210',
        isValid: true,
        sources: ['Dukcapil Database', 'KTP Online']
      }
    },
    email: {
      found: true,
      data: {
        email: query,
        isValid: true,
        domain: query.split('@')[1],
        socialMedia: options.includeSocialMedia ? [
          { platform: 'Facebook', username: 'john.doe', verified: false },
          { platform: 'LinkedIn', username: 'johndoe', verified: true },
        ] : [],
        dataBreaches: options.includeDataBreaches ? [
          { name: 'LinkedIn Breach 2021', date: '2021-06-01', severity: 'High' },
          { name: 'Facebook Leak 2019', date: '2019-04-01', severity: 'Medium' },
        ] : [],
        sources: ['HIBP Database', 'Social Media Scan']
      }
    },
    phone: {
      found: true,
      data: {
        phone: query,
        carrier: 'Telkomsel',
        type: 'Mobile',
        location: 'Jakarta, Indonesia',
        isActive: true,
        socialMedia: options.includeSocialMedia ? [
          { platform: 'WhatsApp', verified: true },
          { platform: 'Telegram', username: '@johndoe', verified: false },
        ] : [],
        sources: ['Carrier Database', 'Social Media Scan']
      }
    },
    domain: {
      found: true,
      data: {
        domain: query,
        registrar: 'GoDaddy',
        registrationDate: '2020-01-01',
        expirationDate: '2025-01-01',
        nameservers: ['ns1.example.com', 'ns2.example.com'],
        whoisData: {
          registrant: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890'
        },
        subdomains: ['www', 'mail', 'ftp', 'api'],
        technologies: [
          { name: 'Apache', version: '2.4.41', category: 'Web Server' },
          { name: 'PHP', version: '7.4', category: 'Programming Language' }
        ],
        sources: ['WHOIS Database', 'DNS Records', 'Technology Scan']
      }
    }
  };

  return mockResults[type as keyof typeof mockResults] || {
    found: false,
    message: 'Data tidak ditemukan atau tidak tersedia'
  };
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getAuthenticatedUser(request);

    // Parse and validate request body
    const body = await request.json();
    const validatedData = osintSearchSchema.parse(body);
    const { type, query, options } = validatedData;

    // Check user plan limits
    const { PLAN_LIMITS } = require('@/constants/plans');
    const userLimits = PLAN_LIMITS[user.plan];
    
    if (userLimits && userLimits.osintSearchPerDay !== -1) {
      // Check daily usage (simplified - in real app, check from database)
      const dailyUsage = 0; // Get from database
      if (dailyUsage >= userLimits.osintSearchPerDay) {
        return NextResponse.json({
          success: false,
          error: 'Kuota pencarian OSINT harian telah habis',
          code: 'QUOTA_EXCEEDED'
        }, { status: 429 });
      }
    }

    // Generate search ID
    const searchId = randomBytes(16).toString('hex');

    // Create search record
    await executeQuery(
      `INSERT INTO osint_searches (id, user_id, type, query, status, metadata)
       VALUES (?, ?, ?, ?, 'processing', ?)`,
      [searchId, user.id, type, query, JSON.stringify(options)]
    );

    // Perform OSINT search
    const searchResults = await performOSINTSearch(type, query, options);

    // Update search record with results
    await executeQuery(
      `UPDATE osint_searches 
       SET status = ?, results = ?, completed_at = NOW()
       WHERE id = ?`,
      [
        searchResults.found ? 'completed' : 'failed',
        JSON.stringify(searchResults),
        searchId
      ]
    );

    // Index results in Elasticsearch for future searches
    if (searchResults.found) {
      try {
        await indexDocument('osint_results', searchId, {
          userId: user.id,
          type,
          query,
          results: searchResults,
          timestamp: new Date().toISOString(),
        });
      } catch (esError) {
        console.error('Elasticsearch indexing error:', esError);
        // Don't fail the request if ES indexing fails
      }
    }

    // Update user API usage
    await executeQuery(
      'UPDATE users SET api_usage = api_usage + 1 WHERE id = ?',
      [user.id]
    );

    return NextResponse.json({
      success: true,
      data: {
        id: searchId,
        type,
        query,
        status: searchResults.found ? 'completed' : 'failed',
        results: searchResults,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
      },
      message: searchResults.found ? 'Pencarian berhasil' : 'Data tidak ditemukan'
    });

  } catch (error: any) {
    console.error('OSINT search error:', error);

    // Handle validation errors
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        error: 'Data tidak valid',
        errors: error.errors.reduce((acc: any, err: any) => {
          acc[err.path[0]] = [err.message];
          return acc;
        }, {})
      }, { status: 400 });
    }

    // Handle auth errors
    if (error.message.includes('Authentication')) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      }, { status: 401 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Gagal melakukan pencarian OSINT',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
