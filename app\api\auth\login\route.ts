import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser, logUserActivity } from '@/lib/auth';
import { z } from 'zod';

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Email tidak valid'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = loginSchema.parse(body);
    const { email, password } = validatedData;

    // Get client info
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Authenticate user
    const loginResult = await authenticateUser(email, password);

    // Log successful login
    await logUserActivity(
      loginResult.user.id,
      'login',
      'User logged in successfully',
      { method: 'email_password' },
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      data: loginResult,
      message: 'Login berhasil'
    });

  } catch (error: any) {
    console.error('Login error:', error);

    // Handle validation errors
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        error: 'Data tidak valid',
        errors: error.errors.reduce((acc: any, err: any) => {
          acc[err.path[0]] = [err.message];
          return acc;
        }, {})
      }, { status: 400 });
    }

    // Handle auth errors
    if (error.name === 'AuthError') {
      return NextResponse.json({
        success: false,
        error: error.message,
        code: 'INVALID_CREDENTIALS'
      }, { status: 401 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan server',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
