import { NextRequest, NextResponse } from 'next/server';
import { getUserByToken, validateApiKey } from '@/lib/auth';
import { executeQuery } from '@/lib/database';

// Get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  // Try JWT token first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    const user = await getUserByToken(token);
    if (user) return user;
  }

  // Try API key
  const apiKey = request.headers.get('x-api-key');
  if (apiKey) {
    const user = await validateApiKey(apiKey);
    if (user) return user;
  }

  throw new Error('Authentication required');
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getAuthenticatedUser(request);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    // Build WHERE clause
    let whereClause = 'WHERE user_id = ?';
    const queryParams: any[] = [user.id];

    if (type) {
      whereClause += ' AND type = ?';
      queryParams.push(type);
    }

    if (status) {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM osint_searches 
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // Calculate pagination
    const offset = (page - 1) * limit;
    const totalPages = Math.ceil(total / limit);

    // Get search history
    const historyQuery = `
      SELECT 
        id,
        type,
        query,
        status,
        metadata,
        created_at,
        completed_at,
        CASE 
          WHEN results IS NOT NULL THEN JSON_EXTRACT(results, '$.found')
          ELSE false
        END as found
      FROM osint_searches 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const searches = await executeQuery(
      historyQuery, 
      [...queryParams, limit, offset]
    );

    // Format results
    const formattedSearches = searches.map((search: any) => ({
      id: search.id,
      type: search.type,
      query: search.query,
      status: search.status,
      found: Boolean(search.found),
      metadata: search.metadata ? JSON.parse(search.metadata) : null,
      createdAt: search.created_at,
      completedAt: search.completed_at,
    }));

    return NextResponse.json({
      success: true,
      data: {
        searches: formattedSearches,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      },
      message: 'Riwayat pencarian berhasil diambil'
    });

  } catch (error: any) {
    console.error('Get OSINT history error:', error);

    // Handle auth errors
    if (error.message.includes('Authentication')) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required',
        code: 'UNAUTHORIZED'
      }, { status: 401 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Gagal mengambil riwayat pencarian',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
