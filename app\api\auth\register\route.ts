import { NextRequest, NextResponse } from 'next/server';
import { createUser, logUserActivity } from '@/lib/auth';
import { z } from 'zod';

// Validation schema
const registerSchema = z.object({
  username: z.string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username hanya boleh mengandung huruf, angka, dan underscore'),
  email: z.string().email('Email tidak valid'),
  password: z.string()
    .min(8, 'Password minimal 8 karakter')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password harus mengandung huruf besar, huruf kecil, dan angka'),
  fullName: z.string()
    .min(2, '<PERSON>a lengkap minimal 2 karakter')
    .max(255, '<PERSON><PERSON> lengkap maksimal 255 karakter'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Password tidak cocok",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = registerSchema.parse(body);
    const { username, email, password, fullName } = validatedData;

    // Get client info
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create user
    const user = await createUser({
      username,
      email,
      password,
      fullName,
    });

    // Log registration
    await logUserActivity(
      user.id,
      'register',
      'User registered successfully',
      { method: 'email_password' },
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          role: user.role,
          plan: user.plan,
        }
      },
      message: 'Registrasi berhasil! Silakan login untuk melanjutkan.'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Registration error:', error);

    // Handle validation errors
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        error: 'Data tidak valid',
        errors: error.errors.reduce((acc: any, err: any) => {
          acc[err.path[0]] = [err.message];
          return acc;
        }, {})
      }, { status: 400 });
    }

    // Handle auth errors (user already exists)
    if (error.name === 'AuthError') {
      return NextResponse.json({
        success: false,
        error: error.message,
        code: 'USER_EXISTS'
      }, { status: 409 });
    }

    // Handle database errors
    if (error.name === 'DatabaseError') {
      return NextResponse.json({
        success: false,
        error: 'Gagal menyimpan data pengguna',
        code: 'DATABASE_ERROR'
      }, { status: 500 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan server',
      code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
