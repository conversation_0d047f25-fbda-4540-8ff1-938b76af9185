version: '3.8'

services:
  # KodeXGuard Application
  kodexguard:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://kodexguard:kodexguard123@mysql:3306/kodexguard
      - REDIS_URL=redis://redis:6379
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - NEXT_PUBLIC_API_URL=http://localhost:3000/api
    depends_on:
      - mysql
      - redis
      - elasticsearch
    volumes:
      - ./uploads:/usr/src/app/uploads
    restart: unless-stopped
    networks:
      - kodexguard-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword123
      - MYSQL_DATABASE=kodexguard
      - MYSQL_USER=kodexguard
      - MYSQL_PASSWORD=kodexguard123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docs/database-schema.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - kodexguard-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - kodexguard-network
    command: redis-server --appendonly yes

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - kodexguard-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - kodexguard
    restart: unless-stopped
    networks:
      - kodexguard-network

volumes:
  mysql_data:
  redis_data:
  elasticsearch_data:

networks:
  kodexguard-network:
    driver: bridge
