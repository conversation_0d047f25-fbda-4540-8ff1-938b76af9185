'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Shield, 
  Search, 
  ArrowLeft,
  AlertTriangle,
  Calendar,
  TrendingUp,
  Filter,
  ExternalLink,
  Bug,
  Clock,
  Star
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function CVEIntelligence() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState('all');
  const [selectedYear, setSelectedYear] = useState('all');
  const [cveData, setCveData] = useState<any[]>([]);
  const [latestCVEs, setLatestCVEs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const severityOptions = [
    { value: 'all', label: 'Semua Severity' },
    { value: 'critical', label: 'Critical' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
  ];

  const yearOptions = [
    { value: 'all', label: 'Semua Tahun' },
    { value: '2024', label: '2024' },
    { value: '2023', label: '2023' },
    { value: '2022', label: '2022' },
    { value: '2021', label: '2021' },
  ];

  // Mock CVE data
  const mockCVEs = [
    {
      id: 'CVE-2024-1234',
      description: 'Remote code execution vulnerability in Apache HTTP Server',
      severity: 'critical',
      cvssScore: 9.8,
      publishedDate: '2024-01-15',
      modifiedDate: '2024-01-16',
      affectedProducts: ['Apache HTTP Server 2.4.x'],
      exploitAvailable: true,
      references: [
        'https://httpd.apache.org/security/vulnerabilities_24.html',
        'https://nvd.nist.gov/vuln/detail/CVE-2024-1234'
      ]
    },
    {
      id: 'CVE-2024-5678',
      description: 'SQL injection vulnerability in WordPress plugin',
      severity: 'high',
      cvssScore: 8.5,
      publishedDate: '2024-01-10',
      modifiedDate: '2024-01-12',
      affectedProducts: ['WordPress Plugin XYZ < 3.2.1'],
      exploitAvailable: false,
      references: [
        'https://wordpress.org/plugins/xyz/',
        'https://nvd.nist.gov/vuln/detail/CVE-2024-5678'
      ]
    },
    {
      id: 'CVE-2024-9012',
      description: 'Cross-site scripting (XSS) vulnerability in React component',
      severity: 'medium',
      cvssScore: 6.1,
      publishedDate: '2024-01-08',
      modifiedDate: '2024-01-09',
      affectedProducts: ['React Component Library < 2.1.0'],
      exploitAvailable: false,
      references: [
        'https://github.com/react-component/library',
        'https://nvd.nist.gov/vuln/detail/CVE-2024-9012'
      ]
    },
    {
      id: 'CVE-2024-3456',
      description: 'Buffer overflow vulnerability in OpenSSL',
      severity: 'high',
      cvssScore: 7.8,
      publishedDate: '2024-01-05',
      modifiedDate: '2024-01-06',
      affectedProducts: ['OpenSSL 3.0.x', 'OpenSSL 1.1.1x'],
      exploitAvailable: true,
      references: [
        'https://www.openssl.org/news/secadv/',
        'https://nvd.nist.gov/vuln/detail/CVE-2024-3456'
      ]
    }
  ];

  useEffect(() => {
    // Load latest CVEs on component mount
    setLatestCVEs(mockCVEs.slice(0, 5));
  }, []);

  const handleSearch = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      let filteredCVEs = [...mockCVEs];
      
      if (searchQuery) {
        filteredCVEs = filteredCVEs.filter(cve => 
          cve.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          cve.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          cve.affectedProducts.some(product => 
            product.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );
      }
      
      if (selectedSeverity !== 'all') {
        filteredCVEs = filteredCVEs.filter(cve => cve.severity === selectedSeverity);
      }
      
      if (selectedYear !== 'all') {
        filteredCVEs = filteredCVEs.filter(cve => 
          cve.publishedDate.startsWith(selectedYear)
        );
      }
      
      setCveData(filteredCVEs);
      setIsLoading(false);
    }, 1000);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-gray-200 dark:border-gray-700 bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/dashboard/panel-user">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <Bug className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">CVE Intelligence</h1>
                <p className="text-muted-foreground">Database kerentanan dan exploit terbaru</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/cve/alerts">CVE Alerts</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Search & Filters */}
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Pencarian CVE</h2>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="md:col-span-2">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Cari CVE, produk, atau deskripsi..."
                    className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-background focus:border-primary focus:outline-none"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <select
                  value={selectedSeverity}
                  onChange={(e) => setSelectedSeverity(e.target.value)}
                  className="px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-background focus:border-primary focus:outline-none"
                >
                  {severityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-background focus:border-primary focus:outline-none"
                >
                  {yearOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <Button onClick={handleSearch} disabled={isLoading} variant="cyber">
                <Search className="h-4 w-4 mr-2" />
                {isLoading ? 'Mencari...' : 'Cari CVE'}
              </Button>
            </div>

            {/* Search Results */}
            {cveData.length > 0 && (
              <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold mb-4">
                  Hasil Pencarian ({cveData.length} CVE ditemukan)
                </h3>
                <div className="space-y-4">
                  {cveData.map((cve) => (
                    <CVECard key={cve.id} cve={cve} getSeverityColor={getSeverityColor} formatDate={formatDate} />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Latest CVEs */}
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                CVE Terbaru
              </h3>
              <div className="space-y-3">
                {latestCVEs.map((cve) => (
                  <div key={cve.id} className="p-3 bg-secondary/20 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-sm">{cve.id}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(cve.severity)}`}>
                        {cve.severity}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                      {cve.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>CVSS: {cve.cvssScore}</span>
                      <span>{formatDate(cve.publishedDate)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* CVE Stats */}
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Statistik CVE
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total CVE 2024:</span>
                  <span className="font-medium">1,234</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Critical:</span>
                  <span className="font-medium text-red-500">89</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">High:</span>
                  <span className="font-medium text-orange-500">456</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Medium:</span>
                  <span className="font-medium text-yellow-500">567</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Low:</span>
                  <span className="font-medium text-green-500">122</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-card rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/cve/alerts">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Setup CVE Alerts
                  </Link>
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/cve/feed">
                    <Calendar className="h-4 w-4 mr-2" />
                    CVE Feed
                  </Link>
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/cve/export">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Export Data
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function CVECard({ cve, getSeverityColor, formatDate }: {
  cve: any;
  getSeverityColor: (severity: string) => string;
  formatDate: (date: string) => string;
}) {
  return (
    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-primary/40 transition-colors">
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-semibold text-lg flex items-center">
            {cve.id}
            {cve.exploitAvailable && (
              <Star className="h-4 w-4 ml-2 text-red-500" title="Exploit Available" />
            )}
          </h4>
          <p className="text-muted-foreground mt-1">{cve.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(cve.severity)}`}>
            {cve.severity.toUpperCase()}
          </span>
          <span className="text-sm font-medium">CVSS: {cve.cvssScore}</span>
        </div>
      </div>
      
      <div className="mb-3">
        <p className="text-sm text-muted-foreground mb-1">
          <strong>Affected Products:</strong>
        </p>
        <div className="flex flex-wrap gap-1">
          {cve.affectedProducts.map((product: string, index: number) => (
            <span key={index} className="px-2 py-1 bg-secondary/20 rounded text-xs">
              {product}
            </span>
          ))}
        </div>
      </div>
      
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>Published: {formatDate(cve.publishedDate)}</span>
        <div className="flex space-x-2">
          {cve.references.slice(0, 2).map((ref: string, index: number) => (
            <a
              key={index}
              href={ref}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline flex items-center"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Reference
            </a>
          ))}
        </div>
      </div>
    </div>
  );
}
